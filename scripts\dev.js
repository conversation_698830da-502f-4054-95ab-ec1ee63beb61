#!/usr/bin/env node

const { spawn } = require("child_process");
const path = require("path");
const fs = require("fs");

// Colors for console output
const colors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function checkEnvFile(envPath, serviceName) {
  if (!fs.existsSync(envPath)) {
    log(`❌ Missing ${envPath} for ${serviceName}`, colors.red);
    log(`   Please copy from ${envPath}.example and configure`, colors.yellow);
    return false;
  }
  return true;
}

function waitForPort(port, timeout = 30000) {
  return new Promise((resolve) => {
    const startTime = Date.now();

    function check() {
      const net = require("net");
      const socket = new net.Socket();

      socket.setTimeout(1000);
      socket.on("connect", () => {
        socket.destroy();
        resolve(true);
      });

      socket.on("timeout", () => {
        socket.destroy();
        checkAgain();
      });

      socket.on("error", () => {
        checkAgain();
      });

      socket.connect(port, "localhost");

      function checkAgain() {
        if (Date.now() - startTime < timeout) {
          setTimeout(check, 1000);
        } else {
          resolve(false);
        }
      }
    }

    check();
  });
}

async function startDevelopmentServers() {
  log(
    "🚀 Starting ChewyAI Development Environment",
    colors.bright + colors.cyan
  );
  log("==========================================", colors.cyan);

  // Check environment files
  const backendEnvPath = path.join(__dirname, "../backend/.env");
  const frontendEnvPath = path.join(__dirname, "../frontend/.env");

  let envOk = true;
  envOk = checkEnvFile(backendEnvPath, "Backend") && envOk;

  if (fs.existsSync(frontendEnvPath)) {
    log("✅ Frontend .env file found", colors.green);
  } else {
    log("ℹ️  Frontend .env file not found (optional)", colors.yellow);
  }

  if (!envOk) {
    log(
      "\n❌ Environment setup incomplete. Please fix the above issues.",
      colors.red
    );
    process.exit(1);
  }

  log("\n📦 Installing dependencies...", colors.blue);

  // Install backend dependencies
  log("   Installing backend dependencies...", colors.blue);
  const backendInstall = spawn("npm", ["install"], {
    cwd: path.join(__dirname, "../backend"),
    stdio: "inherit",
    shell: true,
  });

  await new Promise((resolve, reject) => {
    backendInstall.on("close", (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error("Backend install failed"));
      }
    });
    backendInstall.on("error", reject);
  });

  // Install frontend dependencies
  log("   Installing frontend dependencies...", colors.blue);
  const frontendInstall = spawn("npm", ["install"], {
    cwd: path.join(__dirname, "../frontend"),
    stdio: "inherit",
    shell: true,
  });

  await new Promise((resolve, reject) => {
    frontendInstall.on("close", (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error("Frontend install failed"));
      }
    });
    frontendInstall.on("error", reject);
  });

  log("✅ Dependencies installed", colors.green);

  // Start backend server
  log("\n🔧 Starting backend server on port 3001...", colors.magenta);
  const backend = spawn("npm", ["run", "dev"], {
    cwd: path.join(__dirname, "../backend"),
    stdio: "pipe",
    shell: true,
  });

  backend.stdout.on("data", (data) => {
    const output = data.toString().trim();
    if (output) {
      log(`[Backend] ${output}`, colors.magenta);
    }
  });

  backend.stderr.on("data", (data) => {
    const output = data.toString().trim();
    if (output && !output.includes("DeprecationWarning")) {
      log(`[Backend Error] ${output}`, colors.red);
    }
  });

  // Wait for backend to be ready
  log("   Waiting for backend to be ready...", colors.yellow);
  const backendReady = await waitForPort(3001);

  if (!backendReady) {
    log("❌ Backend failed to start within 30 seconds", colors.red);
    backend.kill();
    process.exit(1);
  }

  log("✅ Backend server is ready!", colors.green);

  // Start frontend server
  log("\n🎨 Starting frontend server on port 3000...", colors.cyan);
  const frontend = spawn("npm", ["run", "dev"], {
    cwd: path.join(__dirname, "../frontend"),
    stdio: "pipe",
    shell: true,
  });

  frontend.stdout.on("data", (data) => {
    const output = data.toString().trim();
    if (output) {
      log(`[Frontend] ${output}`, colors.cyan);
    }
  });

  frontend.stderr.on("data", (data) => {
    const output = data.toString().trim();
    if (output && !output.includes("DeprecationWarning")) {
      log(`[Frontend Error] ${output}`, colors.red);
    }
  });

  // Wait for frontend to be ready
  log("   Waiting for frontend to be ready...", colors.yellow);
  const frontendReady = await waitForPort(3000);

  if (!frontendReady) {
    log("❌ Frontend failed to start within 30 seconds", colors.red);
    frontend.kill();
    backend.kill();
    process.exit(1);
  }

  log("\n🎉 Development environment is ready!", colors.bright + colors.green);
  log("==========================================", colors.green);
  log("🌐 Frontend: http://localhost:3000", colors.cyan);
  log("🔧 Backend:  http://localhost:3001", colors.magenta);
  log("📊 Health:   http://localhost:3001/api/health", colors.blue);
  log("\nPress Ctrl+C to stop all servers", colors.yellow);

  // Handle graceful shutdown
  process.on("SIGINT", () => {
    log("\n🛑 Shutting down development servers...", colors.yellow);
    frontend.kill();
    backend.kill();
    process.exit(0);
  });

  // Keep the process alive
  await new Promise(() => {});
}

// Run the development environment
startDevelopmentServers().catch((error) => {
  log(
    `❌ Failed to start development environment: ${error.message}`,
    colors.red
  );
  process.exit(1);
});
