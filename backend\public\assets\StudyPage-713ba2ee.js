import{r as x,j as e,u as Q,a as B,B as j,e as R}from"./index-fc4201ba.js";import{useStudyStore as L}from"./studyStore-eb933c64.js";import{u as H}from"./useUserSettings-b6794e0e.js";const O=({message:i,priority:d="polite",clearAfter:t=3e3})=>{const[n,g]=x.useState("");return x.useEffect(()=>{if(i&&(g(i),t>0)){const f=setTimeout(()=>{g("")},t);return()=>clearTimeout(f)}},[i,t]),e.jsx("div",{"aria-live":d,"aria-atomic":"true",className:"sr-only",role:"status",children:n})},U=()=>{const[i,d]=x.useState(""),[t,n]=x.useState("polite");return{announce:(v,w="polite")=>{n(w),d(v)},AnnouncementComponent:()=>e.jsx(O,{message:i,priority:t})}},G=x.memo(()=>{var o;const i=Q(),{alert:d}=B(),{currentSession:t,studySetContent:n,nextItem:g,previousItem:f,toggleFlag:v,markReviewed:w,endStudySession:p,updateTimeSpent:N,undo:m,redo:k,canUndo:F,canRedo:C}=L(),[y,l]=x.useState(!1),[A,T]=x.useState(Date.now()),{announce:b,AnnouncementComponent:D}=U();if(x.useEffect(()=>{const u=setInterval(()=>{N(1)},1e3);return()=>clearInterval(u)},[N]),x.useEffect(()=>{l(!1),T(Date.now())},[t==null?void 0:t.currentIndex]),x.useEffect(()=>{const u=s=>{var z;if(!(s.target instanceof HTMLInputElement||s.target instanceof HTMLTextAreaElement||s.target instanceof HTMLSelectElement||((z=s.target)==null?void 0:z.contentEditable)==="true"))if(s.key==="ArrowLeft"){if(s.preventDefault(),t){f();const I=t.currentIndex===0?t.totalItems:t.currentIndex;b(`Card ${I} of ${t.totalItems}`)}}else if(s.key==="ArrowRight"){if(s.preventDefault(),t){g();const I=t.currentIndex===t.totalItems-1?1:t.currentIndex+2;b(`Card ${I} of ${t.totalItems}`)}}else if(s.key===" "||s.key==="ArrowUp"||s.key==="ArrowDown")s.preventDefault(),l(!y);else if(s.key==="f"||s.key==="F"){if(s.preventDefault(),t&&(n!=null&&n.flashcards)){const I=n.flashcards[t.currentIndex];if(I){v(I.id);const P=t.flaggedItems.includes(I.id);b(P?"Card unflagged":"Card flagged for review")}}}else(s.ctrlKey||s.metaKey)&&s.key==="z"&&!s.shiftKey?(s.preventDefault(),F&&m()):(s.ctrlKey||s.metaKey)&&(s.key==="y"||s.key==="z"&&s.shiftKey)&&(s.preventDefault(),C&&k())};return document.addEventListener("keydown",u),()=>document.removeEventListener("keydown",u)},[t,n,y,F,C,f,g,v,m,k,b]),!t||!(n!=null&&n.flashcards))return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No flashcard session found"}),e.jsx(j,{onClick:()=>i("/dashboard"),variant:"secondary",children:"Back to Study Sets"})]})});const S=n.flashcards[t.currentIndex],_=(t.currentIndex+1)/t.totalItems*100,E=t.currentIndex===0,M=t.currentIndex===t.totalItems-1,$=t.flaggedItems.includes(S.id),q=()=>{y&&w(S.id),g();const u=M?1:t.currentIndex+2;b(`Card ${u} of ${t.totalItems}`)},r=()=>{f();const u=E?t.totalItems:t.currentIndex;b(`Card ${u} of ${t.totalItems}`)},c=()=>{v(S.id),b(`Card ${$?"unflagged":"flagged"}`)},a=()=>{l(!y),b(y?"Showing front of card":"Showing back of card")},h=async()=>{const u=Math.floor((Date.now()-A)/1e3),s=t.reviewedItems.length,z=t.flaggedItems.length;p(),await d({title:"Study Session Complete!",message:`Reviewed: ${s}/${t.totalItems} cards
Flagged: ${z} cards
Time spent: ${Math.floor(u/60)}m ${u%60}s`,variant:"success",confirmText:"Continue"}),i(`/study-sets/${t.studySetId}`)};return e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("button",{onClick:()=>i(`/study-sets/${t.studySetId}`),className:"text-gray-400 hover:text-white flex items-center",children:"← Back to Study Set"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-xl font-semibold text-white",children:(o=n.studySet)==null?void 0:o.name}),e.jsxs("p",{className:"text-sm text-gray-400",children:["Card ",t.currentIndex+1," of ",t.totalItems]})]}),e.jsx(j,{onClick:h,variant:"secondary",size:"sm",children:"Finish"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:`${_}%`}})}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[e.jsxs("span",{children:["Progress: ",Math.round(_),"%"]}),e.jsxs("span",{children:["Time: ",Math.floor(t.timeSpent/60),":",(t.timeSpent%60).toString().padStart(2,"0")]})]})]}),e.jsx("div",{className:"mb-8 flashcard-container",children:e.jsxs("div",{className:`
            relative w-full min-h-[24rem] max-h-[32rem] cursor-pointer transition-transform duration-500 transform-style-preserve-3d
            ${y?"rotate-y-180":""}
          `,onClick:a,role:"button",tabIndex:0,"aria-label":`Flashcard ${t.currentIndex+1} of ${t.totalItems}. ${y?"Showing back":"Showing front"}. Click or press space to flip.`,onKeyDown:u=>{(u.key===" "||u.key==="Enter")&&(u.preventDefault(),a())},children:[e.jsx("div",{className:`
            absolute inset-0 w-full h-full backface-hidden
            bg-background-secondary border border-gray-600 rounded-lg p-6 sm:p-8
            flex flex-col justify-center text-center overflow-y-auto
          `,children:e.jsxs("div",{className:"flex-1 flex flex-col justify-center min-h-0",children:[e.jsx("div",{className:"text-sm text-gray-400 mb-4 flex-shrink-0",children:"FRONT"}),e.jsx("div",{className:"text-lg sm:text-xl text-white leading-relaxed break-words overflow-y-auto flex-1 flex items-center justify-center",children:e.jsx("div",{className:"max-w-full",children:S.front})}),!y&&e.jsx("div",{className:"text-sm text-gray-500 mt-6 flex-shrink-0",children:"Click to reveal answer"})]})}),e.jsx("div",{className:`
            absolute inset-0 w-full h-full backface-hidden rotate-y-180
            bg-primary-500/10 border border-primary-500/30 rounded-lg p-6 sm:p-8
            flex flex-col justify-center text-center overflow-y-auto
          `,children:e.jsxs("div",{className:"flex-1 flex flex-col justify-center min-h-0",children:[e.jsx("div",{className:"text-sm text-primary-400 mb-4 flex-shrink-0",children:"BACK"}),e.jsx("div",{className:"text-lg sm:text-xl text-white leading-relaxed break-words overflow-y-auto flex-1 flex items-center justify-center",children:e.jsx("div",{className:"max-w-full",children:S.back})}),y&&e.jsx("div",{className:"text-sm text-gray-500 mt-6 flex-shrink-0",children:"Click to flip back"})]})})]})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(j,{onClick:r,variant:"secondary",children:"← Previous"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(j,{onClick:c,variant:$?"primary":"secondary",size:"sm",children:$?"🚩 Flagged":"🏳️ Flag"}),e.jsx(j,{onClick:()=>l(!y),variant:"secondary",children:y?"Show Front":"Show Back"})]}),e.jsx(j,{onClick:q,variant:"primary",children:"Next →"})]}),e.jsx("div",{className:"mt-8 text-center text-sm text-gray-500",children:e.jsx("p",{children:"Keyboard shortcuts: ← → (navigate) • Space (flip) • F (flag)"})}),e.jsx(D,{})]})}),J=()=>{var q;const i=Q(),{alert:d}=B(),{currentSession:t,studySetContent:n,nextItem:g,submitQuizAnswer:f,endStudySession:v,updateTimeSpent:w}=L(),[p,N]=x.useState([]),[m,k]=x.useState(!1),[F,C]=x.useState(!1);if(x.useEffect(()=>{const r=setInterval(()=>{w(1)},1e3);return()=>clearInterval(r)},[w]),x.useEffect(()=>{N([]),k(!1),C(!1)},[t==null?void 0:t.currentIndex]),!t||!(n!=null&&n.questions))return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No quiz session found"}),e.jsx(j,{onClick:()=>i("/dashboard"),variant:"secondary",children:"Back to Dashboard"})]})});const l=n.questions[t.currentIndex],A=(t.currentIndex+1)/t.totalItems*100,T=t.currentIndex===t.totalItems-1,b=r=>{m||(l.question_type==="multiple_choice"||l.question_type==="true_false"?N([r]):l.question_type==="select_all"&&N(c=>c.includes(r)?c.filter(a=>a!==r):[...c,r]))},D=r=>{m||N([r])},K=()=>{if(m||p.length===0)return;const r=S();f(l.id,p,r),k(!0),C(!0)},S=()=>{var c;const r=l.correct_answers;if(l.question_type==="short_answer"){const a=((c=p[0])==null?void 0:c.toLowerCase().trim())||"";return r.some(h=>a.includes(h.toLowerCase().trim())||h.toLowerCase().trim().includes(a))}else return p.length===r.length&&p.every(a=>r.includes(a))},_=()=>{T?E():g()},E=async()=>{const r=t.totalItems,c=t.correctAnswers||0,a=Math.round(c/r*100),h=t.timeSpent;v(),await d({title:"Quiz Complete!",message:`Score: ${c}/${r} (${a}%)
Time spent: ${Math.floor(h/60)}m ${h%60}s`,variant:"success",confirmText:"Continue"}),i(`/study-sets/${t.studySetId}`)},M=()=>l.options?l.options.map((r,c)=>{const a=p.includes(r),h=l.correct_answers.includes(r);let o="w-full text-left p-4 rounded-lg border transition-all ";return m?h?o+="border-green-500 bg-green-500/20 text-green-400":a&&!h?o+="border-red-500 bg-red-500/20 text-red-400":o+="border-gray-600 bg-background-secondary text-gray-400":a?o+="border-primary-500 bg-primary-500/20 text-primary-400":o+="border-gray-600 bg-background-secondary text-white hover:border-gray-500",e.jsx("button",{onClick:()=>b(r),disabled:m,className:o,children:e.jsxs("div",{className:"flex items-center space-x-3",children:[l.question_type==="multiple_choice"?e.jsx("div",{className:`
                w-5 h-5 rounded-full border-2 flex items-center justify-center
                ${a?"border-current":"border-gray-500"}
              `,children:a&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-current"})}):e.jsx("div",{className:`
                w-5 h-5 rounded border-2 flex items-center justify-center
                ${a?"border-current":"border-gray-500"}
              `,children:a&&e.jsx("div",{className:"w-2 h-2 rounded bg-current"})}),e.jsx("span",{children:r})]})},c)}):null,$=()=>["True","False"].map(c=>{const a=p.includes(c),h=l.correct_answers.includes(c);let o="w-full text-left p-4 rounded-lg border transition-all ";return m?h?o+="border-green-500 bg-green-500/20 text-green-400":a&&!h?o+="border-red-500 bg-red-500/20 text-red-400":o+="border-gray-600 bg-background-secondary text-gray-400":a?o+="border-primary-500 bg-primary-500/20 text-primary-400":o+="border-gray-600 bg-background-secondary text-white hover:border-gray-500",e.jsx("button",{onClick:()=>b(c),disabled:m,className:o,children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`
              w-5 h-5 rounded-full border-2 flex items-center justify-center
              ${a?"border-current":"border-gray-500"}
            `,children:a&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-current"})}),e.jsx("span",{className:"text-lg font-medium",children:c})]})},c)});return e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("button",{onClick:()=>i(`/study-sets/${t.studySetId}`),className:"text-gray-400 hover:text-white flex items-center",children:"← Back to Study Set"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-xl font-semibold text-white",children:(q=n.studySet)==null?void 0:q.name}),e.jsxs("p",{className:"text-sm text-gray-400",children:["Question ",t.currentIndex+1," of ",t.totalItems]})]}),e.jsx(j,{onClick:E,variant:"secondary",size:"sm",children:"Finish Quiz"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:`${A}%`}})}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[e.jsxs("span",{children:["Progress: ",Math.round(A),"%"]}),e.jsxs("span",{children:["Score: ",t.correctAnswers||0,"/",t.currentIndex+(m?1:0)]}),e.jsxs("span",{children:["Time: ",Math.floor(t.timeSpent/60),":",(t.timeSpent%60).toString().padStart(2,"0")]})]})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsx("div",{className:"mb-4",children:e.jsx("span",{className:"text-sm text-gray-400 uppercase tracking-wide",children:l.question_type.replace("_"," ")})}),e.jsx("h2",{className:"text-xl text-white mb-6 leading-relaxed",children:l.question_text}),e.jsx("div",{className:"space-y-3",children:l.question_type==="short_answer"?e.jsx("textarea",{value:p[0]||"",onChange:r=>D(r.target.value),disabled:m,placeholder:"Type your answer here...",rows:3,className:"w-full px-4 py-3 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"}):l.question_type==="true_false"?$():M()}),!m&&e.jsx("div",{className:"mt-6",children:e.jsx(j,{onClick:K,disabled:p.length===0,className:"w-full",children:"Submit Answer"})}),m&&F&&l.explanation&&e.jsxs("div",{className:"mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg",children:[e.jsx("h4",{className:"text-blue-400 font-medium mb-2",children:"Explanation"}),e.jsx("p",{className:"text-gray-300",children:l.explanation})]})]}),m&&e.jsx("div",{className:"flex justify-center",children:e.jsx(j,{onClick:_,variant:"primary",size:"lg",children:T?"Finish Quiz":"Next Question →"})})]})},Y=()=>{const{id:i,mode:d}=R(),t=Q(),{currentSession:n,startStudySession:g}=L(),{settings:f}=H();return x.useEffect(()=>{if(!n||n.studySetId!==i||n.type!==d)if(i&&d&&(d==="flashcards"||d==="quiz")){const v=(f==null?void 0:f.shuffle_flashcards)||!1;g(i,d,v).catch(w=>{console.error("Failed to start study session:",w),alert(w.message||"Failed to start study session"),t(`/study-sets/${i}`)})}else t("/dashboard")},[i,d,n,g,t,f]),n?d==="flashcards"?e.jsx(G,{}):d==="quiz"?e.jsx(J,{}):(t("/dashboard"),null):e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),e.jsx("span",{className:"ml-3 text-gray-400",children:"Starting study session..."})]})})};export{Y as StudyPage};
