function Lv(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();var nt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Jf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Iv(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}),n}var Qf={exports:{}},Bo={},Yf={exports:{}},V={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var is=Symbol.for("react.element"),Nv=Symbol.for("react.portal"),Mv=Symbol.for("react.fragment"),Dv=Symbol.for("react.strict_mode"),$v=Symbol.for("react.profiler"),Vv=Symbol.for("react.provider"),zv=Symbol.for("react.context"),Bv=Symbol.for("react.forward_ref"),Uv=Symbol.for("react.suspense"),Fv=Symbol.for("react.memo"),Hv=Symbol.for("react.lazy"),ud=Symbol.iterator;function Wv(e){return e===null||typeof e!="object"?null:(e=ud&&e[ud]||e["@@iterator"],typeof e=="function"?e:null)}var Xf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Zf=Object.assign,ep={};function Kr(e,t,n){this.props=e,this.context=t,this.refs=ep,this.updater=n||Xf}Kr.prototype.isReactComponent={};Kr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Kr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function tp(){}tp.prototype=Kr.prototype;function Mu(e,t,n){this.props=e,this.context=t,this.refs=ep,this.updater=n||Xf}var Du=Mu.prototype=new tp;Du.constructor=Mu;Zf(Du,Kr.prototype);Du.isPureReactComponent=!0;var cd=Array.isArray,np=Object.prototype.hasOwnProperty,$u={current:null},rp={key:!0,ref:!0,__self:!0,__source:!0};function ip(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)np.call(t,r)&&!rp.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:is,type:e,key:s,ref:o,props:i,_owner:$u.current}}function Kv(e,t){return{$$typeof:is,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Vu(e){return typeof e=="object"&&e!==null&&e.$$typeof===is}function Gv(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var dd=/\/+/g;function Ca(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Gv(""+e.key):t.toString(36)}function zs(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case is:case Nv:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+Ca(o,0):r,cd(i)?(n="",e!=null&&(n=e.replace(dd,"$&/")+"/"),zs(i,t,n,"",function(u){return u})):i!=null&&(Vu(i)&&(i=Kv(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(dd,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",cd(e))for(var a=0;a<e.length;a++){s=e[a];var l=r+Ca(s,a);o+=zs(s,t,n,l,i)}else if(l=Wv(e),typeof l=="function")for(e=l.call(e),a=0;!(s=e.next()).done;)s=s.value,l=r+Ca(s,a++),o+=zs(s,t,n,l,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function gs(e,t,n){if(e==null)return e;var r=[],i=0;return zs(e,r,"","",function(s){return t.call(n,s,i++)}),r}function qv(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Pe={current:null},Bs={transition:null},Jv={ReactCurrentDispatcher:Pe,ReactCurrentBatchConfig:Bs,ReactCurrentOwner:$u};function sp(){throw Error("act(...) is not supported in production builds of React.")}V.Children={map:gs,forEach:function(e,t,n){gs(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return gs(e,function(){t++}),t},toArray:function(e){return gs(e,function(t){return t})||[]},only:function(e){if(!Vu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};V.Component=Kr;V.Fragment=Mv;V.Profiler=$v;V.PureComponent=Mu;V.StrictMode=Dv;V.Suspense=Uv;V.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Jv;V.act=sp;V.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Zf({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=$u.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)np.call(t,l)&&!rp.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:is,type:e.type,key:i,ref:s,props:r,_owner:o}};V.createContext=function(e){return e={$$typeof:zv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Vv,_context:e},e.Consumer=e};V.createElement=ip;V.createFactory=function(e){var t=ip.bind(null,e);return t.type=e,t};V.createRef=function(){return{current:null}};V.forwardRef=function(e){return{$$typeof:Bv,render:e}};V.isValidElement=Vu;V.lazy=function(e){return{$$typeof:Hv,_payload:{_status:-1,_result:e},_init:qv}};V.memo=function(e,t){return{$$typeof:Fv,type:e,compare:t===void 0?null:t}};V.startTransition=function(e){var t=Bs.transition;Bs.transition={};try{e()}finally{Bs.transition=t}};V.unstable_act=sp;V.useCallback=function(e,t){return Pe.current.useCallback(e,t)};V.useContext=function(e){return Pe.current.useContext(e)};V.useDebugValue=function(){};V.useDeferredValue=function(e){return Pe.current.useDeferredValue(e)};V.useEffect=function(e,t){return Pe.current.useEffect(e,t)};V.useId=function(){return Pe.current.useId()};V.useImperativeHandle=function(e,t,n){return Pe.current.useImperativeHandle(e,t,n)};V.useInsertionEffect=function(e,t){return Pe.current.useInsertionEffect(e,t)};V.useLayoutEffect=function(e,t){return Pe.current.useLayoutEffect(e,t)};V.useMemo=function(e,t){return Pe.current.useMemo(e,t)};V.useReducer=function(e,t,n){return Pe.current.useReducer(e,t,n)};V.useRef=function(e){return Pe.current.useRef(e)};V.useState=function(e){return Pe.current.useState(e)};V.useSyncExternalStore=function(e,t,n){return Pe.current.useSyncExternalStore(e,t,n)};V.useTransition=function(){return Pe.current.useTransition()};V.version="18.3.1";Yf.exports=V;var S=Yf.exports;const Fe=Jf(S),Qv=Lv({__proto__:null,default:Fe},[S]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yv=S,Xv=Symbol.for("react.element"),Zv=Symbol.for("react.fragment"),ey=Object.prototype.hasOwnProperty,ty=Yv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ny={key:!0,ref:!0,__self:!0,__source:!0};function op(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)ey.call(t,r)&&!ny.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Xv,type:e,key:s,ref:o,props:i,_owner:ty.current}}Bo.Fragment=Zv;Bo.jsx=op;Bo.jsxs=op;Qf.exports=Bo;var g=Qf.exports,ml={},ap={exports:{}},Ge={},lp={exports:{}},up={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,L){var D=P.length;P.push(L);e:for(;0<D;){var N=D-1>>>1,G=P[N];if(0<i(G,L))P[N]=L,P[D]=G,D=N;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var L=P[0],D=P.pop();if(D!==L){P[0]=D;e:for(var N=0,G=P.length,Tn=G>>>1;N<Tn;){var pt=2*(N+1)-1,rr=P[pt],ze=pt+1,Pn=P[ze];if(0>i(rr,D))ze<G&&0>i(Pn,rr)?(P[N]=Pn,P[ze]=D,N=ze):(P[N]=rr,P[pt]=D,N=pt);else if(ze<G&&0>i(Pn,D))P[N]=Pn,P[ze]=D,N=ze;else break e}}return L}function i(P,L){var D=P.sortIndex-L.sortIndex;return D!==0?D:P.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var l=[],u=[],c=1,d=null,h=3,f=!1,y=!1,w=!1,x=typeof setTimeout=="function"?setTimeout:null,v=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(P){for(var L=n(u);L!==null;){if(L.callback===null)r(u);else if(L.startTime<=P)r(u),L.sortIndex=L.expirationTime,t(l,L);else break;L=n(u)}}function _(P){if(w=!1,m(P),!y)if(n(l)!==null)y=!0,ne(k);else{var L=n(u);L!==null&&Je(_,L.startTime-P)}}function k(P,L){y=!1,w&&(w=!1,v(b),b=-1),f=!0;var D=h;try{for(m(L),d=n(l);d!==null&&(!(d.expirationTime>L)||P&&!ue());){var N=d.callback;if(typeof N=="function"){d.callback=null,h=d.priorityLevel;var G=N(d.expirationTime<=L);L=e.unstable_now(),typeof G=="function"?d.callback=G:d===n(l)&&r(l),m(L)}else r(l);d=n(l)}if(d!==null)var Tn=!0;else{var pt=n(u);pt!==null&&Je(_,pt.startTime-L),Tn=!1}return Tn}finally{d=null,h=D,f=!1}}var C=!1,E=null,b=-1,A=5,O=-1;function ue(){return!(e.unstable_now()-O<A)}function pe(){if(E!==null){var P=e.unstable_now();O=P;var L=!0;try{L=E(!0,P)}finally{L?Ce():(C=!1,E=null)}}else C=!1}var Ce;if(typeof p=="function")Ce=function(){p(pe)};else if(typeof MessageChannel<"u"){var ce=new MessageChannel,Ht=ce.port2;ce.port1.onmessage=pe,Ce=function(){Ht.postMessage(null)}}else Ce=function(){x(pe,0)};function ne(P){E=P,C||(C=!0,Ce())}function Je(P,L){b=x(function(){P(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){y||f||(y=!0,ne(k))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):A=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(P){switch(h){case 1:case 2:case 3:var L=3;break;default:L=h}var D=h;h=L;try{return P()}finally{h=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,L){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var D=h;h=P;try{return L()}finally{h=D}},e.unstable_scheduleCallback=function(P,L,D){var N=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?N+D:N):D=N,P){case 1:var G=-1;break;case 2:G=250;break;case 5:G=**********;break;case 4:G=1e4;break;default:G=5e3}return G=D+G,P={id:c++,callback:L,priorityLevel:P,startTime:D,expirationTime:G,sortIndex:-1},D>N?(P.sortIndex=D,t(u,P),n(l)===null&&P===n(u)&&(w?(v(b),b=-1):w=!0,Je(_,D-N))):(P.sortIndex=G,t(l,P),y||f||(y=!0,ne(k))),P},e.unstable_shouldYield=ue,e.unstable_wrapCallback=function(P){var L=h;return function(){var D=h;h=L;try{return P.apply(this,arguments)}finally{h=D}}}})(up);lp.exports=up;var ry=lp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var iy=S,We=ry;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var cp=new Set,Mi={};function Xn(e,t){Ir(e,t),Ir(e+"Capture",t)}function Ir(e,t){for(Mi[e]=t,e=0;e<t.length;e++)cp.add(t[e])}var Nt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),gl=Object.prototype.hasOwnProperty,sy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,hd={},fd={};function oy(e){return gl.call(fd,e)?!0:gl.call(hd,e)?!1:sy.test(e)?fd[e]=!0:(hd[e]=!0,!1)}function ay(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ly(e,t,n,r){if(t===null||typeof t>"u"||ay(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function je(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var ye={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ye[e]=new je(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ye[t]=new je(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ye[e]=new je(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ye[e]=new je(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ye[e]=new je(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ye[e]=new je(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ye[e]=new je(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ye[e]=new je(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ye[e]=new je(e,5,!1,e.toLowerCase(),null,!1,!1)});var zu=/[\-:]([a-z])/g;function Bu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(zu,Bu);ye[t]=new je(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(zu,Bu);ye[t]=new je(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(zu,Bu);ye[t]=new je(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ye[e]=new je(e,1,!1,e.toLowerCase(),null,!1,!1)});ye.xlinkHref=new je("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ye[e]=new je(e,1,!1,e.toLowerCase(),null,!0,!0)});function Uu(e,t,n,r){var i=ye.hasOwnProperty(t)?ye[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ly(t,n,i,r)&&(n=null),r||i===null?oy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Bt=iy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,vs=Symbol.for("react.element"),ur=Symbol.for("react.portal"),cr=Symbol.for("react.fragment"),Fu=Symbol.for("react.strict_mode"),vl=Symbol.for("react.profiler"),dp=Symbol.for("react.provider"),hp=Symbol.for("react.context"),Hu=Symbol.for("react.forward_ref"),yl=Symbol.for("react.suspense"),wl=Symbol.for("react.suspense_list"),Wu=Symbol.for("react.memo"),Gt=Symbol.for("react.lazy"),fp=Symbol.for("react.offscreen"),pd=Symbol.iterator;function ei(e){return e===null||typeof e!="object"?null:(e=pd&&e[pd]||e["@@iterator"],typeof e=="function"?e:null)}var Z=Object.assign,Ea;function di(e){if(Ea===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ea=t&&t[1]||""}return`
`+Ea+e}var ba=!1;function Ta(e,t){if(!e||ba)return"";ba=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,a=s.length-1;1<=o&&0<=a&&i[o]!==s[a];)a--;for(;1<=o&&0<=a;o--,a--)if(i[o]!==s[a]){if(o!==1||a!==1)do if(o--,a--,0>a||i[o]!==s[a]){var l=`
`+i[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=a);break}}}finally{ba=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?di(e):""}function uy(e){switch(e.tag){case 5:return di(e.type);case 16:return di("Lazy");case 13:return di("Suspense");case 19:return di("SuspenseList");case 0:case 2:case 15:return e=Ta(e.type,!1),e;case 11:return e=Ta(e.type.render,!1),e;case 1:return e=Ta(e.type,!0),e;default:return""}}function xl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case cr:return"Fragment";case ur:return"Portal";case vl:return"Profiler";case Fu:return"StrictMode";case yl:return"Suspense";case wl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case hp:return(e.displayName||"Context")+".Consumer";case dp:return(e._context.displayName||"Context")+".Provider";case Hu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Wu:return t=e.displayName||null,t!==null?t:xl(e.type)||"Memo";case Gt:t=e._payload,e=e._init;try{return xl(e(t))}catch{}}return null}function cy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return xl(t);case 8:return t===Fu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function gn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function pp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function dy(e){var t=pp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ys(e){e._valueTracker||(e._valueTracker=dy(e))}function mp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=pp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function no(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Sl(e,t){var n=t.checked;return Z({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function md(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=gn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function gp(e,t){t=t.checked,t!=null&&Uu(e,"checked",t,!1)}function _l(e,t){gp(e,t);var n=gn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?kl(e,t.type,n):t.hasOwnProperty("defaultValue")&&kl(e,t.type,gn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function gd(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function kl(e,t,n){(t!=="number"||no(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var hi=Array.isArray;function br(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+gn(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Cl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return Z({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function vd(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(hi(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:gn(n)}}function vp(e,t){var n=gn(t.value),r=gn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function yd(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function yp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function El(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?yp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ws,wp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ws=ws||document.createElement("div"),ws.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ws.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Di(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Si={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hy=["Webkit","ms","Moz","O"];Object.keys(Si).forEach(function(e){hy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Si[t]=Si[e]})});function xp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Si.hasOwnProperty(e)&&Si[e]?(""+t).trim():t+"px"}function Sp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=xp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var fy=Z({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function bl(e,t){if(t){if(fy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function Tl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pl=null;function Ku(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var jl=null,Tr=null,Pr=null;function wd(e){if(e=as(e)){if(typeof jl!="function")throw Error(T(280));var t=e.stateNode;t&&(t=Ko(t),jl(e.stateNode,e.type,t))}}function _p(e){Tr?Pr?Pr.push(e):Pr=[e]:Tr=e}function kp(){if(Tr){var e=Tr,t=Pr;if(Pr=Tr=null,wd(e),t)for(e=0;e<t.length;e++)wd(t[e])}}function Cp(e,t){return e(t)}function Ep(){}var Pa=!1;function bp(e,t,n){if(Pa)return e(t,n);Pa=!0;try{return Cp(e,t,n)}finally{Pa=!1,(Tr!==null||Pr!==null)&&(Ep(),kp())}}function $i(e,t){var n=e.stateNode;if(n===null)return null;var r=Ko(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var Al=!1;if(Nt)try{var ti={};Object.defineProperty(ti,"passive",{get:function(){Al=!0}}),window.addEventListener("test",ti,ti),window.removeEventListener("test",ti,ti)}catch{Al=!1}function py(e,t,n,r,i,s,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var _i=!1,ro=null,io=!1,Rl=null,my={onError:function(e){_i=!0,ro=e}};function gy(e,t,n,r,i,s,o,a,l){_i=!1,ro=null,py.apply(my,arguments)}function vy(e,t,n,r,i,s,o,a,l){if(gy.apply(this,arguments),_i){if(_i){var u=ro;_i=!1,ro=null}else throw Error(T(198));io||(io=!0,Rl=u)}}function Zn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Tp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function xd(e){if(Zn(e)!==e)throw Error(T(188))}function yy(e){var t=e.alternate;if(!t){if(t=Zn(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return xd(i),e;if(s===r)return xd(i),t;s=s.sibling}throw Error(T(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o){for(a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o)throw Error(T(189))}}if(n.alternate!==r)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function Pp(e){return e=yy(e),e!==null?jp(e):null}function jp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=jp(e);if(t!==null)return t;e=e.sibling}return null}var Ap=We.unstable_scheduleCallback,Sd=We.unstable_cancelCallback,wy=We.unstable_shouldYield,xy=We.unstable_requestPaint,re=We.unstable_now,Sy=We.unstable_getCurrentPriorityLevel,Gu=We.unstable_ImmediatePriority,Rp=We.unstable_UserBlockingPriority,so=We.unstable_NormalPriority,_y=We.unstable_LowPriority,Op=We.unstable_IdlePriority,Uo=null,wt=null;function ky(e){if(wt&&typeof wt.onCommitFiberRoot=="function")try{wt.onCommitFiberRoot(Uo,e,void 0,(e.current.flags&128)===128)}catch{}}var dt=Math.clz32?Math.clz32:by,Cy=Math.log,Ey=Math.LN2;function by(e){return e>>>=0,e===0?32:31-(Cy(e)/Ey|0)|0}var xs=64,Ss=4194304;function fi(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function oo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~i;a!==0?r=fi(a):(s&=o,s!==0&&(r=fi(s)))}else o=n&~i,o!==0?r=fi(o):s!==0&&(r=fi(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-dt(t),i=1<<n,r|=e[n],t&=~i;return r}function Ty(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Py(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-dt(s),a=1<<o,l=i[o];l===-1?(!(a&n)||a&r)&&(i[o]=Ty(a,t)):l<=t&&(e.expiredLanes|=a),s&=~a}}function Ol(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Lp(){var e=xs;return xs<<=1,!(xs&4194240)&&(xs=64),e}function ja(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ss(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-dt(t),e[t]=n}function jy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-dt(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function qu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-dt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var B=0;function Ip(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Np,Ju,Mp,Dp,$p,Ll=!1,_s=[],rn=null,sn=null,on=null,Vi=new Map,zi=new Map,Yt=[],Ay="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function _d(e,t){switch(e){case"focusin":case"focusout":rn=null;break;case"dragenter":case"dragleave":sn=null;break;case"mouseover":case"mouseout":on=null;break;case"pointerover":case"pointerout":Vi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":zi.delete(t.pointerId)}}function ni(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=as(t),t!==null&&Ju(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Ry(e,t,n,r,i){switch(t){case"focusin":return rn=ni(rn,e,t,n,r,i),!0;case"dragenter":return sn=ni(sn,e,t,n,r,i),!0;case"mouseover":return on=ni(on,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Vi.set(s,ni(Vi.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,zi.set(s,ni(zi.get(s)||null,e,t,n,r,i)),!0}return!1}function Vp(e){var t=zn(e.target);if(t!==null){var n=Zn(t);if(n!==null){if(t=n.tag,t===13){if(t=Tp(n),t!==null){e.blockedOn=t,$p(e.priority,function(){Mp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Us(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Il(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Pl=r,n.target.dispatchEvent(r),Pl=null}else return t=as(n),t!==null&&Ju(t),e.blockedOn=n,!1;t.shift()}return!0}function kd(e,t,n){Us(e)&&n.delete(t)}function Oy(){Ll=!1,rn!==null&&Us(rn)&&(rn=null),sn!==null&&Us(sn)&&(sn=null),on!==null&&Us(on)&&(on=null),Vi.forEach(kd),zi.forEach(kd)}function ri(e,t){e.blockedOn===t&&(e.blockedOn=null,Ll||(Ll=!0,We.unstable_scheduleCallback(We.unstable_NormalPriority,Oy)))}function Bi(e){function t(i){return ri(i,e)}if(0<_s.length){ri(_s[0],e);for(var n=1;n<_s.length;n++){var r=_s[n];r.blockedOn===e&&(r.blockedOn=null)}}for(rn!==null&&ri(rn,e),sn!==null&&ri(sn,e),on!==null&&ri(on,e),Vi.forEach(t),zi.forEach(t),n=0;n<Yt.length;n++)r=Yt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Yt.length&&(n=Yt[0],n.blockedOn===null);)Vp(n),n.blockedOn===null&&Yt.shift()}var jr=Bt.ReactCurrentBatchConfig,ao=!0;function Ly(e,t,n,r){var i=B,s=jr.transition;jr.transition=null;try{B=1,Qu(e,t,n,r)}finally{B=i,jr.transition=s}}function Iy(e,t,n,r){var i=B,s=jr.transition;jr.transition=null;try{B=4,Qu(e,t,n,r)}finally{B=i,jr.transition=s}}function Qu(e,t,n,r){if(ao){var i=Il(e,t,n,r);if(i===null)Va(e,t,r,lo,n),_d(e,r);else if(Ry(i,e,t,n,r))r.stopPropagation();else if(_d(e,r),t&4&&-1<Ay.indexOf(e)){for(;i!==null;){var s=as(i);if(s!==null&&Np(s),s=Il(e,t,n,r),s===null&&Va(e,t,r,lo,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Va(e,t,r,null,n)}}var lo=null;function Il(e,t,n,r){if(lo=null,e=Ku(r),e=zn(e),e!==null)if(t=Zn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Tp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return lo=e,null}function zp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Sy()){case Gu:return 1;case Rp:return 4;case so:case _y:return 16;case Op:return 536870912;default:return 16}default:return 16}}var en=null,Yu=null,Fs=null;function Bp(){if(Fs)return Fs;var e,t=Yu,n=t.length,r,i="value"in en?en.value:en.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return Fs=i.slice(e,1<r?1-r:void 0)}function Hs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ks(){return!0}function Cd(){return!1}function qe(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?ks:Cd,this.isPropagationStopped=Cd,this}return Z(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ks)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ks)},persist:function(){},isPersistent:ks}),t}var Gr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Xu=qe(Gr),os=Z({},Gr,{view:0,detail:0}),Ny=qe(os),Aa,Ra,ii,Fo=Z({},os,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Zu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ii&&(ii&&e.type==="mousemove"?(Aa=e.screenX-ii.screenX,Ra=e.screenY-ii.screenY):Ra=Aa=0,ii=e),Aa)},movementY:function(e){return"movementY"in e?e.movementY:Ra}}),Ed=qe(Fo),My=Z({},Fo,{dataTransfer:0}),Dy=qe(My),$y=Z({},os,{relatedTarget:0}),Oa=qe($y),Vy=Z({},Gr,{animationName:0,elapsedTime:0,pseudoElement:0}),zy=qe(Vy),By=Z({},Gr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Uy=qe(By),Fy=Z({},Gr,{data:0}),bd=qe(Fy),Hy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Wy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ky={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Gy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ky[e])?!!t[e]:!1}function Zu(){return Gy}var qy=Z({},os,{key:function(e){if(e.key){var t=Hy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Hs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Wy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Zu,charCode:function(e){return e.type==="keypress"?Hs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Hs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Jy=qe(qy),Qy=Z({},Fo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Td=qe(Qy),Yy=Z({},os,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Zu}),Xy=qe(Yy),Zy=Z({},Gr,{propertyName:0,elapsedTime:0,pseudoElement:0}),e1=qe(Zy),t1=Z({},Fo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),n1=qe(t1),r1=[9,13,27,32],ec=Nt&&"CompositionEvent"in window,ki=null;Nt&&"documentMode"in document&&(ki=document.documentMode);var i1=Nt&&"TextEvent"in window&&!ki,Up=Nt&&(!ec||ki&&8<ki&&11>=ki),Pd=String.fromCharCode(32),jd=!1;function Fp(e,t){switch(e){case"keyup":return r1.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Hp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var dr=!1;function s1(e,t){switch(e){case"compositionend":return Hp(t);case"keypress":return t.which!==32?null:(jd=!0,Pd);case"textInput":return e=t.data,e===Pd&&jd?null:e;default:return null}}function o1(e,t){if(dr)return e==="compositionend"||!ec&&Fp(e,t)?(e=Bp(),Fs=Yu=en=null,dr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Up&&t.locale!=="ko"?null:t.data;default:return null}}var a1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ad(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!a1[e.type]:t==="textarea"}function Wp(e,t,n,r){_p(r),t=uo(t,"onChange"),0<t.length&&(n=new Xu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ci=null,Ui=null;function l1(e){n0(e,0)}function Ho(e){var t=pr(e);if(mp(t))return e}function u1(e,t){if(e==="change")return t}var Kp=!1;if(Nt){var La;if(Nt){var Ia="oninput"in document;if(!Ia){var Rd=document.createElement("div");Rd.setAttribute("oninput","return;"),Ia=typeof Rd.oninput=="function"}La=Ia}else La=!1;Kp=La&&(!document.documentMode||9<document.documentMode)}function Od(){Ci&&(Ci.detachEvent("onpropertychange",Gp),Ui=Ci=null)}function Gp(e){if(e.propertyName==="value"&&Ho(Ui)){var t=[];Wp(t,Ui,e,Ku(e)),bp(l1,t)}}function c1(e,t,n){e==="focusin"?(Od(),Ci=t,Ui=n,Ci.attachEvent("onpropertychange",Gp)):e==="focusout"&&Od()}function d1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ho(Ui)}function h1(e,t){if(e==="click")return Ho(t)}function f1(e,t){if(e==="input"||e==="change")return Ho(t)}function p1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ft=typeof Object.is=="function"?Object.is:p1;function Fi(e,t){if(ft(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!gl.call(t,i)||!ft(e[i],t[i]))return!1}return!0}function Ld(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Id(e,t){var n=Ld(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ld(n)}}function qp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?qp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Jp(){for(var e=window,t=no();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=no(e.document)}return t}function tc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function m1(e){var t=Jp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&qp(n.ownerDocument.documentElement,n)){if(r!==null&&tc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=Id(n,s);var o=Id(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var g1=Nt&&"documentMode"in document&&11>=document.documentMode,hr=null,Nl=null,Ei=null,Ml=!1;function Nd(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ml||hr==null||hr!==no(r)||(r=hr,"selectionStart"in r&&tc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Ei&&Fi(Ei,r)||(Ei=r,r=uo(Nl,"onSelect"),0<r.length&&(t=new Xu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=hr)))}function Cs(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var fr={animationend:Cs("Animation","AnimationEnd"),animationiteration:Cs("Animation","AnimationIteration"),animationstart:Cs("Animation","AnimationStart"),transitionend:Cs("Transition","TransitionEnd")},Na={},Qp={};Nt&&(Qp=document.createElement("div").style,"AnimationEvent"in window||(delete fr.animationend.animation,delete fr.animationiteration.animation,delete fr.animationstart.animation),"TransitionEvent"in window||delete fr.transitionend.transition);function Wo(e){if(Na[e])return Na[e];if(!fr[e])return e;var t=fr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Qp)return Na[e]=t[n];return e}var Yp=Wo("animationend"),Xp=Wo("animationiteration"),Zp=Wo("animationstart"),e0=Wo("transitionend"),t0=new Map,Md="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function xn(e,t){t0.set(e,t),Xn(t,[e])}for(var Ma=0;Ma<Md.length;Ma++){var Da=Md[Ma],v1=Da.toLowerCase(),y1=Da[0].toUpperCase()+Da.slice(1);xn(v1,"on"+y1)}xn(Yp,"onAnimationEnd");xn(Xp,"onAnimationIteration");xn(Zp,"onAnimationStart");xn("dblclick","onDoubleClick");xn("focusin","onFocus");xn("focusout","onBlur");xn(e0,"onTransitionEnd");Ir("onMouseEnter",["mouseout","mouseover"]);Ir("onMouseLeave",["mouseout","mouseover"]);Ir("onPointerEnter",["pointerout","pointerover"]);Ir("onPointerLeave",["pointerout","pointerover"]);Xn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Xn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Xn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Xn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Xn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Xn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var pi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),w1=new Set("cancel close invalid load scroll toggle".split(" ").concat(pi));function Dd(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,vy(r,t,void 0,e),e.currentTarget=null}function n0(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==s&&i.isPropagationStopped())break e;Dd(i,a,u),s=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==s&&i.isPropagationStopped())break e;Dd(i,a,u),s=l}}}if(io)throw e=Rl,io=!1,Rl=null,e}function W(e,t){var n=t[Bl];n===void 0&&(n=t[Bl]=new Set);var r=e+"__bubble";n.has(r)||(r0(t,e,2,!1),n.add(r))}function $a(e,t,n){var r=0;t&&(r|=4),r0(n,e,r,t)}var Es="_reactListening"+Math.random().toString(36).slice(2);function Hi(e){if(!e[Es]){e[Es]=!0,cp.forEach(function(n){n!=="selectionchange"&&(w1.has(n)||$a(n,!1,e),$a(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Es]||(t[Es]=!0,$a("selectionchange",!1,t))}}function r0(e,t,n,r){switch(zp(t)){case 1:var i=Ly;break;case 4:i=Iy;break;default:i=Qu}n=i.bind(null,t,n,e),i=void 0,!Al||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Va(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;o=o.return}for(;a!==null;){if(o=zn(a),o===null)return;if(l=o.tag,l===5||l===6){r=s=o;continue e}a=a.parentNode}}r=r.return}bp(function(){var u=s,c=Ku(n),d=[];e:{var h=t0.get(e);if(h!==void 0){var f=Xu,y=e;switch(e){case"keypress":if(Hs(n)===0)break e;case"keydown":case"keyup":f=Jy;break;case"focusin":y="focus",f=Oa;break;case"focusout":y="blur",f=Oa;break;case"beforeblur":case"afterblur":f=Oa;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":f=Ed;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":f=Dy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":f=Xy;break;case Yp:case Xp:case Zp:f=zy;break;case e0:f=e1;break;case"scroll":f=Ny;break;case"wheel":f=n1;break;case"copy":case"cut":case"paste":f=Uy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":f=Td}var w=(t&4)!==0,x=!w&&e==="scroll",v=w?h!==null?h+"Capture":null:h;w=[];for(var p=u,m;p!==null;){m=p;var _=m.stateNode;if(m.tag===5&&_!==null&&(m=_,v!==null&&(_=$i(p,v),_!=null&&w.push(Wi(p,_,m)))),x)break;p=p.return}0<w.length&&(h=new f(h,y,null,n,c),d.push({event:h,listeners:w}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",f=e==="mouseout"||e==="pointerout",h&&n!==Pl&&(y=n.relatedTarget||n.fromElement)&&(zn(y)||y[Mt]))break e;if((f||h)&&(h=c.window===c?c:(h=c.ownerDocument)?h.defaultView||h.parentWindow:window,f?(y=n.relatedTarget||n.toElement,f=u,y=y?zn(y):null,y!==null&&(x=Zn(y),y!==x||y.tag!==5&&y.tag!==6)&&(y=null)):(f=null,y=u),f!==y)){if(w=Ed,_="onMouseLeave",v="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(w=Td,_="onPointerLeave",v="onPointerEnter",p="pointer"),x=f==null?h:pr(f),m=y==null?h:pr(y),h=new w(_,p+"leave",f,n,c),h.target=x,h.relatedTarget=m,_=null,zn(c)===u&&(w=new w(v,p+"enter",y,n,c),w.target=m,w.relatedTarget=x,_=w),x=_,f&&y)t:{for(w=f,v=y,p=0,m=w;m;m=ir(m))p++;for(m=0,_=v;_;_=ir(_))m++;for(;0<p-m;)w=ir(w),p--;for(;0<m-p;)v=ir(v),m--;for(;p--;){if(w===v||v!==null&&w===v.alternate)break t;w=ir(w),v=ir(v)}w=null}else w=null;f!==null&&$d(d,h,f,w,!1),y!==null&&x!==null&&$d(d,x,y,w,!0)}}e:{if(h=u?pr(u):window,f=h.nodeName&&h.nodeName.toLowerCase(),f==="select"||f==="input"&&h.type==="file")var k=u1;else if(Ad(h))if(Kp)k=f1;else{k=d1;var C=c1}else(f=h.nodeName)&&f.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(k=h1);if(k&&(k=k(e,u))){Wp(d,k,n,c);break e}C&&C(e,h,u),e==="focusout"&&(C=h._wrapperState)&&C.controlled&&h.type==="number"&&kl(h,"number",h.value)}switch(C=u?pr(u):window,e){case"focusin":(Ad(C)||C.contentEditable==="true")&&(hr=C,Nl=u,Ei=null);break;case"focusout":Ei=Nl=hr=null;break;case"mousedown":Ml=!0;break;case"contextmenu":case"mouseup":case"dragend":Ml=!1,Nd(d,n,c);break;case"selectionchange":if(g1)break;case"keydown":case"keyup":Nd(d,n,c)}var E;if(ec)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else dr?Fp(e,n)&&(b="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(b="onCompositionStart");b&&(Up&&n.locale!=="ko"&&(dr||b!=="onCompositionStart"?b==="onCompositionEnd"&&dr&&(E=Bp()):(en=c,Yu="value"in en?en.value:en.textContent,dr=!0)),C=uo(u,b),0<C.length&&(b=new bd(b,e,null,n,c),d.push({event:b,listeners:C}),E?b.data=E:(E=Hp(n),E!==null&&(b.data=E)))),(E=i1?s1(e,n):o1(e,n))&&(u=uo(u,"onBeforeInput"),0<u.length&&(c=new bd("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=E))}n0(d,t)})}function Wi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function uo(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=$i(e,n),s!=null&&r.unshift(Wi(e,s,i)),s=$i(e,t),s!=null&&r.push(Wi(e,s,i))),e=e.return}return r}function ir(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function $d(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=$i(n,s),l!=null&&o.unshift(Wi(n,l,a))):i||(l=$i(n,s),l!=null&&o.push(Wi(n,l,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var x1=/\r\n?/g,S1=/\u0000|\uFFFD/g;function Vd(e){return(typeof e=="string"?e:""+e).replace(x1,`
`).replace(S1,"")}function bs(e,t,n){if(t=Vd(t),Vd(e)!==t&&n)throw Error(T(425))}function co(){}var Dl=null,$l=null;function Vl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var zl=typeof setTimeout=="function"?setTimeout:void 0,_1=typeof clearTimeout=="function"?clearTimeout:void 0,zd=typeof Promise=="function"?Promise:void 0,k1=typeof queueMicrotask=="function"?queueMicrotask:typeof zd<"u"?function(e){return zd.resolve(null).then(e).catch(C1)}:zl;function C1(e){setTimeout(function(){throw e})}function za(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Bi(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Bi(t)}function an(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Bd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var qr=Math.random().toString(36).slice(2),yt="__reactFiber$"+qr,Ki="__reactProps$"+qr,Mt="__reactContainer$"+qr,Bl="__reactEvents$"+qr,E1="__reactListeners$"+qr,b1="__reactHandles$"+qr;function zn(e){var t=e[yt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Mt]||n[yt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Bd(e);e!==null;){if(n=e[yt])return n;e=Bd(e)}return t}e=n,n=e.parentNode}return null}function as(e){return e=e[yt]||e[Mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function pr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function Ko(e){return e[Ki]||null}var Ul=[],mr=-1;function Sn(e){return{current:e}}function K(e){0>mr||(e.current=Ul[mr],Ul[mr]=null,mr--)}function F(e,t){mr++,Ul[mr]=e.current,e.current=t}var vn={},ke=Sn(vn),Ne=Sn(!1),Gn=vn;function Nr(e,t){var n=e.type.contextTypes;if(!n)return vn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Me(e){return e=e.childContextTypes,e!=null}function ho(){K(Ne),K(ke)}function Ud(e,t,n){if(ke.current!==vn)throw Error(T(168));F(ke,t),F(Ne,n)}function i0(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(T(108,cy(e)||"Unknown",i));return Z({},n,r)}function fo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||vn,Gn=ke.current,F(ke,e),F(Ne,Ne.current),!0}function Fd(e,t,n){var r=e.stateNode;if(!r)throw Error(T(169));n?(e=i0(e,t,Gn),r.__reactInternalMemoizedMergedChildContext=e,K(Ne),K(ke),F(ke,e)):K(Ne),F(Ne,n)}var Pt=null,Go=!1,Ba=!1;function s0(e){Pt===null?Pt=[e]:Pt.push(e)}function T1(e){Go=!0,s0(e)}function _n(){if(!Ba&&Pt!==null){Ba=!0;var e=0,t=B;try{var n=Pt;for(B=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Pt=null,Go=!1}catch(i){throw Pt!==null&&(Pt=Pt.slice(e+1)),Ap(Gu,_n),i}finally{B=t,Ba=!1}}return null}var gr=[],vr=0,po=null,mo=0,Xe=[],Ze=0,qn=null,jt=1,At="";function On(e,t){gr[vr++]=mo,gr[vr++]=po,po=e,mo=t}function o0(e,t,n){Xe[Ze++]=jt,Xe[Ze++]=At,Xe[Ze++]=qn,qn=e;var r=jt;e=At;var i=32-dt(r)-1;r&=~(1<<i),n+=1;var s=32-dt(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,jt=1<<32-dt(t)+i|n<<i|r,At=s+e}else jt=1<<s|n<<i|r,At=e}function nc(e){e.return!==null&&(On(e,1),o0(e,1,0))}function rc(e){for(;e===po;)po=gr[--vr],gr[vr]=null,mo=gr[--vr],gr[vr]=null;for(;e===qn;)qn=Xe[--Ze],Xe[Ze]=null,At=Xe[--Ze],Xe[Ze]=null,jt=Xe[--Ze],Xe[Ze]=null}var He=null,Ue=null,q=!1,ct=null;function a0(e,t){var n=et(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Hd(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,He=e,Ue=an(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,He=e,Ue=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=qn!==null?{id:jt,overflow:At}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=et(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,He=e,Ue=null,!0):!1;default:return!1}}function Fl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Hl(e){if(q){var t=Ue;if(t){var n=t;if(!Hd(e,t)){if(Fl(e))throw Error(T(418));t=an(n.nextSibling);var r=He;t&&Hd(e,t)?a0(r,n):(e.flags=e.flags&-4097|2,q=!1,He=e)}}else{if(Fl(e))throw Error(T(418));e.flags=e.flags&-4097|2,q=!1,He=e}}}function Wd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;He=e}function Ts(e){if(e!==He)return!1;if(!q)return Wd(e),q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Vl(e.type,e.memoizedProps)),t&&(t=Ue)){if(Fl(e))throw l0(),Error(T(418));for(;t;)a0(e,t),t=an(t.nextSibling)}if(Wd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ue=an(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ue=null}}else Ue=He?an(e.stateNode.nextSibling):null;return!0}function l0(){for(var e=Ue;e;)e=an(e.nextSibling)}function Mr(){Ue=He=null,q=!1}function ic(e){ct===null?ct=[e]:ct.push(e)}var P1=Bt.ReactCurrentBatchConfig;function si(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var r=n.stateNode}if(!r)throw Error(T(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var a=i.refs;o===null?delete a[s]:a[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function Ps(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Kd(e){var t=e._init;return t(e._payload)}function u0(e){function t(v,p){if(e){var m=v.deletions;m===null?(v.deletions=[p],v.flags|=16):m.push(p)}}function n(v,p){if(!e)return null;for(;p!==null;)t(v,p),p=p.sibling;return null}function r(v,p){for(v=new Map;p!==null;)p.key!==null?v.set(p.key,p):v.set(p.index,p),p=p.sibling;return v}function i(v,p){return v=dn(v,p),v.index=0,v.sibling=null,v}function s(v,p,m){return v.index=m,e?(m=v.alternate,m!==null?(m=m.index,m<p?(v.flags|=2,p):m):(v.flags|=2,p)):(v.flags|=1048576,p)}function o(v){return e&&v.alternate===null&&(v.flags|=2),v}function a(v,p,m,_){return p===null||p.tag!==6?(p=qa(m,v.mode,_),p.return=v,p):(p=i(p,m),p.return=v,p)}function l(v,p,m,_){var k=m.type;return k===cr?c(v,p,m.props.children,_,m.key):p!==null&&(p.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Gt&&Kd(k)===p.type)?(_=i(p,m.props),_.ref=si(v,p,m),_.return=v,_):(_=Ys(m.type,m.key,m.props,null,v.mode,_),_.ref=si(v,p,m),_.return=v,_)}function u(v,p,m,_){return p===null||p.tag!==4||p.stateNode.containerInfo!==m.containerInfo||p.stateNode.implementation!==m.implementation?(p=Ja(m,v.mode,_),p.return=v,p):(p=i(p,m.children||[]),p.return=v,p)}function c(v,p,m,_,k){return p===null||p.tag!==7?(p=Wn(m,v.mode,_,k),p.return=v,p):(p=i(p,m),p.return=v,p)}function d(v,p,m){if(typeof p=="string"&&p!==""||typeof p=="number")return p=qa(""+p,v.mode,m),p.return=v,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case vs:return m=Ys(p.type,p.key,p.props,null,v.mode,m),m.ref=si(v,null,p),m.return=v,m;case ur:return p=Ja(p,v.mode,m),p.return=v,p;case Gt:var _=p._init;return d(v,_(p._payload),m)}if(hi(p)||ei(p))return p=Wn(p,v.mode,m,null),p.return=v,p;Ps(v,p)}return null}function h(v,p,m,_){var k=p!==null?p.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return k!==null?null:a(v,p,""+m,_);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case vs:return m.key===k?l(v,p,m,_):null;case ur:return m.key===k?u(v,p,m,_):null;case Gt:return k=m._init,h(v,p,k(m._payload),_)}if(hi(m)||ei(m))return k!==null?null:c(v,p,m,_,null);Ps(v,m)}return null}function f(v,p,m,_,k){if(typeof _=="string"&&_!==""||typeof _=="number")return v=v.get(m)||null,a(p,v,""+_,k);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case vs:return v=v.get(_.key===null?m:_.key)||null,l(p,v,_,k);case ur:return v=v.get(_.key===null?m:_.key)||null,u(p,v,_,k);case Gt:var C=_._init;return f(v,p,m,C(_._payload),k)}if(hi(_)||ei(_))return v=v.get(m)||null,c(p,v,_,k,null);Ps(p,_)}return null}function y(v,p,m,_){for(var k=null,C=null,E=p,b=p=0,A=null;E!==null&&b<m.length;b++){E.index>b?(A=E,E=null):A=E.sibling;var O=h(v,E,m[b],_);if(O===null){E===null&&(E=A);break}e&&E&&O.alternate===null&&t(v,E),p=s(O,p,b),C===null?k=O:C.sibling=O,C=O,E=A}if(b===m.length)return n(v,E),q&&On(v,b),k;if(E===null){for(;b<m.length;b++)E=d(v,m[b],_),E!==null&&(p=s(E,p,b),C===null?k=E:C.sibling=E,C=E);return q&&On(v,b),k}for(E=r(v,E);b<m.length;b++)A=f(E,v,b,m[b],_),A!==null&&(e&&A.alternate!==null&&E.delete(A.key===null?b:A.key),p=s(A,p,b),C===null?k=A:C.sibling=A,C=A);return e&&E.forEach(function(ue){return t(v,ue)}),q&&On(v,b),k}function w(v,p,m,_){var k=ei(m);if(typeof k!="function")throw Error(T(150));if(m=k.call(m),m==null)throw Error(T(151));for(var C=k=null,E=p,b=p=0,A=null,O=m.next();E!==null&&!O.done;b++,O=m.next()){E.index>b?(A=E,E=null):A=E.sibling;var ue=h(v,E,O.value,_);if(ue===null){E===null&&(E=A);break}e&&E&&ue.alternate===null&&t(v,E),p=s(ue,p,b),C===null?k=ue:C.sibling=ue,C=ue,E=A}if(O.done)return n(v,E),q&&On(v,b),k;if(E===null){for(;!O.done;b++,O=m.next())O=d(v,O.value,_),O!==null&&(p=s(O,p,b),C===null?k=O:C.sibling=O,C=O);return q&&On(v,b),k}for(E=r(v,E);!O.done;b++,O=m.next())O=f(E,v,b,O.value,_),O!==null&&(e&&O.alternate!==null&&E.delete(O.key===null?b:O.key),p=s(O,p,b),C===null?k=O:C.sibling=O,C=O);return e&&E.forEach(function(pe){return t(v,pe)}),q&&On(v,b),k}function x(v,p,m,_){if(typeof m=="object"&&m!==null&&m.type===cr&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case vs:e:{for(var k=m.key,C=p;C!==null;){if(C.key===k){if(k=m.type,k===cr){if(C.tag===7){n(v,C.sibling),p=i(C,m.props.children),p.return=v,v=p;break e}}else if(C.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Gt&&Kd(k)===C.type){n(v,C.sibling),p=i(C,m.props),p.ref=si(v,C,m),p.return=v,v=p;break e}n(v,C);break}else t(v,C);C=C.sibling}m.type===cr?(p=Wn(m.props.children,v.mode,_,m.key),p.return=v,v=p):(_=Ys(m.type,m.key,m.props,null,v.mode,_),_.ref=si(v,p,m),_.return=v,v=_)}return o(v);case ur:e:{for(C=m.key;p!==null;){if(p.key===C)if(p.tag===4&&p.stateNode.containerInfo===m.containerInfo&&p.stateNode.implementation===m.implementation){n(v,p.sibling),p=i(p,m.children||[]),p.return=v,v=p;break e}else{n(v,p);break}else t(v,p);p=p.sibling}p=Ja(m,v.mode,_),p.return=v,v=p}return o(v);case Gt:return C=m._init,x(v,p,C(m._payload),_)}if(hi(m))return y(v,p,m,_);if(ei(m))return w(v,p,m,_);Ps(v,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,p!==null&&p.tag===6?(n(v,p.sibling),p=i(p,m),p.return=v,v=p):(n(v,p),p=qa(m,v.mode,_),p.return=v,v=p),o(v)):n(v,p)}return x}var Dr=u0(!0),c0=u0(!1),go=Sn(null),vo=null,yr=null,sc=null;function oc(){sc=yr=vo=null}function ac(e){var t=go.current;K(go),e._currentValue=t}function Wl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ar(e,t){vo=e,sc=yr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ie=!0),e.firstContext=null)}function rt(e){var t=e._currentValue;if(sc!==e)if(e={context:e,memoizedValue:t,next:null},yr===null){if(vo===null)throw Error(T(308));yr=e,vo.dependencies={lanes:0,firstContext:e}}else yr=yr.next=e;return t}var Bn=null;function lc(e){Bn===null?Bn=[e]:Bn.push(e)}function d0(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,lc(t)):(n.next=i.next,i.next=n),t.interleaved=n,Dt(e,r)}function Dt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var qt=!1;function uc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function h0(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ot(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ln(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Dt(e,n)}return i=r.interleaved,i===null?(t.next=t,lc(r)):(t.next=i.next,i.next=t),r.interleaved=t,Dt(e,n)}function Ws(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,qu(e,n)}}function Gd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function yo(e,t,n,r){var i=e.updateQueue;qt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?s=u:o.next=u,o=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==o&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(s!==null){var d=i.baseState;o=0,c=u=l=null,a=s;do{var h=a.lane,f=a.eventTime;if((r&h)===h){c!==null&&(c=c.next={eventTime:f,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var y=e,w=a;switch(h=t,f=n,w.tag){case 1:if(y=w.payload,typeof y=="function"){d=y.call(f,d,h);break e}d=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=w.payload,h=typeof y=="function"?y.call(f,d,h):y,h==null)break e;d=Z({},d,h);break e;case 2:qt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,h=i.effects,h===null?i.effects=[a]:h.push(a))}else f={eventTime:f,lane:h,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=f,l=d):c=c.next=f,o|=h;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;h=a,a=h.next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}while(1);if(c===null&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);Qn|=o,e.lanes=o,e.memoizedState=d}}function qd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(T(191,i));i.call(r)}}}var ls={},xt=Sn(ls),Gi=Sn(ls),qi=Sn(ls);function Un(e){if(e===ls)throw Error(T(174));return e}function cc(e,t){switch(F(qi,t),F(Gi,e),F(xt,ls),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:El(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=El(t,e)}K(xt),F(xt,t)}function $r(){K(xt),K(Gi),K(qi)}function f0(e){Un(qi.current);var t=Un(xt.current),n=El(t,e.type);t!==n&&(F(Gi,e),F(xt,n))}function dc(e){Gi.current===e&&(K(xt),K(Gi))}var Q=Sn(0);function wo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ua=[];function hc(){for(var e=0;e<Ua.length;e++)Ua[e]._workInProgressVersionPrimary=null;Ua.length=0}var Ks=Bt.ReactCurrentDispatcher,Fa=Bt.ReactCurrentBatchConfig,Jn=0,X=null,ae=null,he=null,xo=!1,bi=!1,Ji=0,j1=0;function we(){throw Error(T(321))}function fc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ft(e[n],t[n]))return!1;return!0}function pc(e,t,n,r,i,s){if(Jn=s,X=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ks.current=e===null||e.memoizedState===null?L1:I1,e=n(r,i),bi){s=0;do{if(bi=!1,Ji=0,25<=s)throw Error(T(301));s+=1,he=ae=null,t.updateQueue=null,Ks.current=N1,e=n(r,i)}while(bi)}if(Ks.current=So,t=ae!==null&&ae.next!==null,Jn=0,he=ae=X=null,xo=!1,t)throw Error(T(300));return e}function mc(){var e=Ji!==0;return Ji=0,e}function gt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return he===null?X.memoizedState=he=e:he=he.next=e,he}function it(){if(ae===null){var e=X.alternate;e=e!==null?e.memoizedState:null}else e=ae.next;var t=he===null?X.memoizedState:he.next;if(t!==null)he=t,ae=e;else{if(e===null)throw Error(T(310));ae=e,e={memoizedState:ae.memoizedState,baseState:ae.baseState,baseQueue:ae.baseQueue,queue:ae.queue,next:null},he===null?X.memoizedState=he=e:he=he.next=e}return he}function Qi(e,t){return typeof t=="function"?t(e):t}function Ha(e){var t=it(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=ae,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var a=o=null,l=null,u=s;do{var c=u.lane;if((Jn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,o=r):l=l.next=d,X.lanes|=c,Qn|=c}u=u.next}while(u!==null&&u!==s);l===null?o=r:l.next=a,ft(r,t.memoizedState)||(Ie=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,X.lanes|=s,Qn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Wa(e){var t=it(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);ft(s,t.memoizedState)||(Ie=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function p0(){}function m0(e,t){var n=X,r=it(),i=t(),s=!ft(r.memoizedState,i);if(s&&(r.memoizedState=i,Ie=!0),r=r.queue,gc(y0.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||he!==null&&he.memoizedState.tag&1){if(n.flags|=2048,Yi(9,v0.bind(null,n,r,i,t),void 0,null),fe===null)throw Error(T(349));Jn&30||g0(n,t,i)}return i}function g0(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function v0(e,t,n,r){t.value=n,t.getSnapshot=r,w0(t)&&x0(e)}function y0(e,t,n){return n(function(){w0(t)&&x0(e)})}function w0(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ft(e,n)}catch{return!0}}function x0(e){var t=Dt(e,1);t!==null&&ht(t,e,1,-1)}function Jd(e){var t=gt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Qi,lastRenderedState:e},t.queue=e,e=e.dispatch=O1.bind(null,X,e),[t.memoizedState,e]}function Yi(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function S0(){return it().memoizedState}function Gs(e,t,n,r){var i=gt();X.flags|=e,i.memoizedState=Yi(1|t,n,void 0,r===void 0?null:r)}function qo(e,t,n,r){var i=it();r=r===void 0?null:r;var s=void 0;if(ae!==null){var o=ae.memoizedState;if(s=o.destroy,r!==null&&fc(r,o.deps)){i.memoizedState=Yi(t,n,s,r);return}}X.flags|=e,i.memoizedState=Yi(1|t,n,s,r)}function Qd(e,t){return Gs(8390656,8,e,t)}function gc(e,t){return qo(2048,8,e,t)}function _0(e,t){return qo(4,2,e,t)}function k0(e,t){return qo(4,4,e,t)}function C0(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function E0(e,t,n){return n=n!=null?n.concat([e]):null,qo(4,4,C0.bind(null,t,e),n)}function vc(){}function b0(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&fc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function T0(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&fc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function P0(e,t,n){return Jn&21?(ft(n,t)||(n=Lp(),X.lanes|=n,Qn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ie=!0),e.memoizedState=n)}function A1(e,t){var n=B;B=n!==0&&4>n?n:4,e(!0);var r=Fa.transition;Fa.transition={};try{e(!1),t()}finally{B=n,Fa.transition=r}}function j0(){return it().memoizedState}function R1(e,t,n){var r=cn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},A0(e))R0(t,n);else if(n=d0(e,t,n,r),n!==null){var i=Te();ht(n,e,r,i),O0(n,t,r)}}function O1(e,t,n){var r=cn(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(A0(e))R0(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,a=s(o,n);if(i.hasEagerState=!0,i.eagerState=a,ft(a,o)){var l=t.interleaved;l===null?(i.next=i,lc(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=d0(e,t,i,r),n!==null&&(i=Te(),ht(n,e,r,i),O0(n,t,r))}}function A0(e){var t=e.alternate;return e===X||t!==null&&t===X}function R0(e,t){bi=xo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function O0(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,qu(e,n)}}var So={readContext:rt,useCallback:we,useContext:we,useEffect:we,useImperativeHandle:we,useInsertionEffect:we,useLayoutEffect:we,useMemo:we,useReducer:we,useRef:we,useState:we,useDebugValue:we,useDeferredValue:we,useTransition:we,useMutableSource:we,useSyncExternalStore:we,useId:we,unstable_isNewReconciler:!1},L1={readContext:rt,useCallback:function(e,t){return gt().memoizedState=[e,t===void 0?null:t],e},useContext:rt,useEffect:Qd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Gs(4194308,4,C0.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Gs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Gs(4,2,e,t)},useMemo:function(e,t){var n=gt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=gt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=R1.bind(null,X,e),[r.memoizedState,e]},useRef:function(e){var t=gt();return e={current:e},t.memoizedState=e},useState:Jd,useDebugValue:vc,useDeferredValue:function(e){return gt().memoizedState=e},useTransition:function(){var e=Jd(!1),t=e[0];return e=A1.bind(null,e[1]),gt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=X,i=gt();if(q){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),fe===null)throw Error(T(349));Jn&30||g0(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Qd(y0.bind(null,r,s,e),[e]),r.flags|=2048,Yi(9,v0.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=gt(),t=fe.identifierPrefix;if(q){var n=At,r=jt;n=(r&~(1<<32-dt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ji++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=j1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},I1={readContext:rt,useCallback:b0,useContext:rt,useEffect:gc,useImperativeHandle:E0,useInsertionEffect:_0,useLayoutEffect:k0,useMemo:T0,useReducer:Ha,useRef:S0,useState:function(){return Ha(Qi)},useDebugValue:vc,useDeferredValue:function(e){var t=it();return P0(t,ae.memoizedState,e)},useTransition:function(){var e=Ha(Qi)[0],t=it().memoizedState;return[e,t]},useMutableSource:p0,useSyncExternalStore:m0,useId:j0,unstable_isNewReconciler:!1},N1={readContext:rt,useCallback:b0,useContext:rt,useEffect:gc,useImperativeHandle:E0,useInsertionEffect:_0,useLayoutEffect:k0,useMemo:T0,useReducer:Wa,useRef:S0,useState:function(){return Wa(Qi)},useDebugValue:vc,useDeferredValue:function(e){var t=it();return ae===null?t.memoizedState=e:P0(t,ae.memoizedState,e)},useTransition:function(){var e=Wa(Qi)[0],t=it().memoizedState;return[e,t]},useMutableSource:p0,useSyncExternalStore:m0,useId:j0,unstable_isNewReconciler:!1};function at(e,t){if(e&&e.defaultProps){t=Z({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Kl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Z({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Jo={isMounted:function(e){return(e=e._reactInternals)?Zn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Te(),i=cn(e),s=Ot(r,i);s.payload=t,n!=null&&(s.callback=n),t=ln(e,s,i),t!==null&&(ht(t,e,i,r),Ws(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Te(),i=cn(e),s=Ot(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=ln(e,s,i),t!==null&&(ht(t,e,i,r),Ws(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Te(),r=cn(e),i=Ot(n,r);i.tag=2,t!=null&&(i.callback=t),t=ln(e,i,r),t!==null&&(ht(t,e,r,n),Ws(t,e,r))}};function Yd(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!Fi(n,r)||!Fi(i,s):!0}function L0(e,t,n){var r=!1,i=vn,s=t.contextType;return typeof s=="object"&&s!==null?s=rt(s):(i=Me(t)?Gn:ke.current,r=t.contextTypes,s=(r=r!=null)?Nr(e,i):vn),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Jo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function Xd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Jo.enqueueReplaceState(t,t.state,null)}function Gl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},uc(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=rt(s):(s=Me(t)?Gn:ke.current,i.context=Nr(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Kl(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Jo.enqueueReplaceState(i,i.state,null),yo(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Vr(e,t){try{var n="",r=t;do n+=uy(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function Ka(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ql(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var M1=typeof WeakMap=="function"?WeakMap:Map;function I0(e,t,n){n=Ot(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ko||(ko=!0,iu=r),ql(e,t)},n}function N0(e,t,n){n=Ot(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){ql(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){ql(e,t),typeof r!="function"&&(un===null?un=new Set([this]):un.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Zd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new M1;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Q1.bind(null,e,t,n),t.then(e,e))}function eh(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function th(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ot(-1,1),t.tag=2,ln(n,t,1))),n.lanes|=1),e)}var D1=Bt.ReactCurrentOwner,Ie=!1;function be(e,t,n,r){t.child=e===null?c0(t,null,n,r):Dr(t,e.child,n,r)}function nh(e,t,n,r,i){n=n.render;var s=t.ref;return Ar(t,i),r=pc(e,t,n,r,s,i),n=mc(),e!==null&&!Ie?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,$t(e,t,i)):(q&&n&&nc(t),t.flags|=1,be(e,t,r,i),t.child)}function rh(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!Ec(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,M0(e,t,s,r,i)):(e=Ys(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Fi,n(o,r)&&e.ref===t.ref)return $t(e,t,i)}return t.flags|=1,e=dn(s,r),e.ref=t.ref,e.return=t,t.child=e}function M0(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(Fi(s,r)&&e.ref===t.ref)if(Ie=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Ie=!0);else return t.lanes=e.lanes,$t(e,t,i)}return Jl(e,t,n,r,i)}function D0(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},F(xr,Be),Be|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,F(xr,Be),Be|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,F(xr,Be),Be|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,F(xr,Be),Be|=r;return be(e,t,i,n),t.child}function $0(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Jl(e,t,n,r,i){var s=Me(n)?Gn:ke.current;return s=Nr(t,s),Ar(t,i),n=pc(e,t,n,r,s,i),r=mc(),e!==null&&!Ie?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,$t(e,t,i)):(q&&r&&nc(t),t.flags|=1,be(e,t,n,i),t.child)}function ih(e,t,n,r,i){if(Me(n)){var s=!0;fo(t)}else s=!1;if(Ar(t,i),t.stateNode===null)qs(e,t),L0(t,n,r),Gl(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=rt(u):(u=Me(n)?Gn:ke.current,u=Nr(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";d||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Xd(t,o,r,u),qt=!1;var h=t.memoizedState;o.state=h,yo(t,r,o,i),l=t.memoizedState,a!==r||h!==l||Ne.current||qt?(typeof c=="function"&&(Kl(t,n,c,r),l=t.memoizedState),(a=qt||Yd(t,n,a,r,h,l,u))?(d||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,h0(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:at(t.type,a),o.props=u,d=t.pendingProps,h=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=rt(l):(l=Me(n)?Gn:ke.current,l=Nr(t,l));var f=n.getDerivedStateFromProps;(c=typeof f=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==d||h!==l)&&Xd(t,o,r,l),qt=!1,h=t.memoizedState,o.state=h,yo(t,r,o,i);var y=t.memoizedState;a!==d||h!==y||Ne.current||qt?(typeof f=="function"&&(Kl(t,n,f,r),y=t.memoizedState),(u=qt||Yd(t,n,u,r,h,y,l)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,y,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,y,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),o.props=r,o.state=y,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Ql(e,t,n,r,s,i)}function Ql(e,t,n,r,i,s){$0(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&Fd(t,n,!1),$t(e,t,s);r=t.stateNode,D1.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Dr(t,e.child,null,s),t.child=Dr(t,null,a,s)):be(e,t,a,s),t.memoizedState=r.state,i&&Fd(t,n,!0),t.child}function V0(e){var t=e.stateNode;t.pendingContext?Ud(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ud(e,t.context,!1),cc(e,t.containerInfo)}function sh(e,t,n,r,i){return Mr(),ic(i),t.flags|=256,be(e,t,n,r),t.child}var Yl={dehydrated:null,treeContext:null,retryLane:0};function Xl(e){return{baseLanes:e,cachePool:null,transitions:null}}function z0(e,t,n){var r=t.pendingProps,i=Q.current,s=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),F(Q,i&1),e===null)return Hl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=Xo(o,r,0,null),e=Wn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Xl(n),t.memoizedState=Yl,e):yc(t,o));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return $1(e,t,o,r,a,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=dn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?s=dn(a,s):(s=Wn(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?Xl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=Yl,r}return s=e.child,e=s.sibling,r=dn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function yc(e,t){return t=Xo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function js(e,t,n,r){return r!==null&&ic(r),Dr(t,e.child,null,n),e=yc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function $1(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=Ka(Error(T(422))),js(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=Xo({mode:"visible",children:r.children},i,0,null),s=Wn(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Dr(t,e.child,null,o),t.child.memoizedState=Xl(o),t.memoizedState=Yl,s);if(!(t.mode&1))return js(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(T(419)),r=Ka(s,r,void 0),js(e,t,o,r)}if(a=(o&e.childLanes)!==0,Ie||a){if(r=fe,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Dt(e,i),ht(r,e,i,-1))}return Cc(),r=Ka(Error(T(421))),js(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Y1.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,Ue=an(i.nextSibling),He=t,q=!0,ct=null,e!==null&&(Xe[Ze++]=jt,Xe[Ze++]=At,Xe[Ze++]=qn,jt=e.id,At=e.overflow,qn=t),t=yc(t,r.children),t.flags|=4096,t)}function oh(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Wl(e.return,t,n)}function Ga(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function B0(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(be(e,t,r.children,n),r=Q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&oh(e,n,t);else if(e.tag===19)oh(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(F(Q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&wo(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ga(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&wo(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ga(t,!0,n,null,s);break;case"together":Ga(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function qs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function $t(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Qn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=dn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=dn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function V1(e,t,n){switch(t.tag){case 3:V0(t),Mr();break;case 5:f0(t);break;case 1:Me(t.type)&&fo(t);break;case 4:cc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;F(go,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(F(Q,Q.current&1),t.flags|=128,null):n&t.child.childLanes?z0(e,t,n):(F(Q,Q.current&1),e=$t(e,t,n),e!==null?e.sibling:null);F(Q,Q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return B0(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),F(Q,Q.current),r)break;return null;case 22:case 23:return t.lanes=0,D0(e,t,n)}return $t(e,t,n)}var U0,Zl,F0,H0;U0=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Zl=function(){};F0=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Un(xt.current);var s=null;switch(n){case"input":i=Sl(e,i),r=Sl(e,r),s=[];break;case"select":i=Z({},i,{value:void 0}),r=Z({},r,{value:void 0}),s=[];break;case"textarea":i=Cl(e,i),r=Cl(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=co)}bl(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Mi.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Mi.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&W("scroll",e),s||a===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};H0=function(e,t,n,r){n!==r&&(t.flags|=4)};function oi(e,t){if(!q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function xe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function z1(e,t,n){var r=t.pendingProps;switch(rc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return xe(t),null;case 1:return Me(t.type)&&ho(),xe(t),null;case 3:return r=t.stateNode,$r(),K(Ne),K(ke),hc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ts(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ct!==null&&(au(ct),ct=null))),Zl(e,t),xe(t),null;case 5:dc(t);var i=Un(qi.current);if(n=t.type,e!==null&&t.stateNode!=null)F0(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(T(166));return xe(t),null}if(e=Un(xt.current),Ts(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[yt]=t,r[Ki]=s,e=(t.mode&1)!==0,n){case"dialog":W("cancel",r),W("close",r);break;case"iframe":case"object":case"embed":W("load",r);break;case"video":case"audio":for(i=0;i<pi.length;i++)W(pi[i],r);break;case"source":W("error",r);break;case"img":case"image":case"link":W("error",r),W("load",r);break;case"details":W("toggle",r);break;case"input":md(r,s),W("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},W("invalid",r);break;case"textarea":vd(r,s),W("invalid",r)}bl(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var a=s[o];o==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&bs(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&bs(r.textContent,a,e),i=["children",""+a]):Mi.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&W("scroll",r)}switch(n){case"input":ys(r),gd(r,s,!0);break;case"textarea":ys(r),yd(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=co)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=yp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[yt]=t,e[Ki]=r,U0(e,t,!1,!1),t.stateNode=e;e:{switch(o=Tl(n,r),n){case"dialog":W("cancel",e),W("close",e),i=r;break;case"iframe":case"object":case"embed":W("load",e),i=r;break;case"video":case"audio":for(i=0;i<pi.length;i++)W(pi[i],e);i=r;break;case"source":W("error",e),i=r;break;case"img":case"image":case"link":W("error",e),W("load",e),i=r;break;case"details":W("toggle",e),i=r;break;case"input":md(e,r),i=Sl(e,r),W("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Z({},r,{value:void 0}),W("invalid",e);break;case"textarea":vd(e,r),i=Cl(e,r),W("invalid",e);break;default:i=r}bl(n,i),a=i;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?Sp(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&wp(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Di(e,l):typeof l=="number"&&Di(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Mi.hasOwnProperty(s)?l!=null&&s==="onScroll"&&W("scroll",e):l!=null&&Uu(e,s,l,o))}switch(n){case"input":ys(e),gd(e,r,!1);break;case"textarea":ys(e),yd(e);break;case"option":r.value!=null&&e.setAttribute("value",""+gn(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?br(e,!!r.multiple,s,!1):r.defaultValue!=null&&br(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=co)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return xe(t),null;case 6:if(e&&t.stateNode!=null)H0(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(T(166));if(n=Un(qi.current),Un(xt.current),Ts(t)){if(r=t.stateNode,n=t.memoizedProps,r[yt]=t,(s=r.nodeValue!==n)&&(e=He,e!==null))switch(e.tag){case 3:bs(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&bs(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[yt]=t,t.stateNode=r}return xe(t),null;case 13:if(K(Q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(q&&Ue!==null&&t.mode&1&&!(t.flags&128))l0(),Mr(),t.flags|=98560,s=!1;else if(s=Ts(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(T(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(T(317));s[yt]=t}else Mr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;xe(t),s=!1}else ct!==null&&(au(ct),ct=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Q.current&1?le===0&&(le=3):Cc())),t.updateQueue!==null&&(t.flags|=4),xe(t),null);case 4:return $r(),Zl(e,t),e===null&&Hi(t.stateNode.containerInfo),xe(t),null;case 10:return ac(t.type._context),xe(t),null;case 17:return Me(t.type)&&ho(),xe(t),null;case 19:if(K(Q),s=t.memoizedState,s===null)return xe(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)oi(s,!1);else{if(le!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=wo(e),o!==null){for(t.flags|=128,oi(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return F(Q,Q.current&1|2),t.child}e=e.sibling}s.tail!==null&&re()>zr&&(t.flags|=128,r=!0,oi(s,!1),t.lanes=4194304)}else{if(!r)if(e=wo(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),oi(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!q)return xe(t),null}else 2*re()-s.renderingStartTime>zr&&n!==1073741824&&(t.flags|=128,r=!0,oi(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=re(),t.sibling=null,n=Q.current,F(Q,r?n&1|2:n&1),t):(xe(t),null);case 22:case 23:return kc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Be&1073741824&&(xe(t),t.subtreeFlags&6&&(t.flags|=8192)):xe(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function B1(e,t){switch(rc(t),t.tag){case 1:return Me(t.type)&&ho(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return $r(),K(Ne),K(ke),hc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return dc(t),null;case 13:if(K(Q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));Mr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return K(Q),null;case 4:return $r(),null;case 10:return ac(t.type._context),null;case 22:case 23:return kc(),null;case 24:return null;default:return null}}var As=!1,_e=!1,U1=typeof WeakSet=="function"?WeakSet:Set,j=null;function wr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ee(e,t,r)}else n.current=null}function eu(e,t,n){try{n()}catch(r){ee(e,t,r)}}var ah=!1;function F1(e,t){if(Dl=ao,e=Jp(),tc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,c=0,d=e,h=null;t:for(;;){for(var f;d!==n||i!==0&&d.nodeType!==3||(a=o+i),d!==s||r!==0&&d.nodeType!==3||(l=o+r),d.nodeType===3&&(o+=d.nodeValue.length),(f=d.firstChild)!==null;)h=d,d=f;for(;;){if(d===e)break t;if(h===n&&++u===i&&(a=o),h===s&&++c===r&&(l=o),(f=d.nextSibling)!==null)break;d=h,h=d.parentNode}d=f}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for($l={focusedElem:e,selectionRange:n},ao=!1,j=t;j!==null;)if(t=j,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,j=e;else for(;j!==null;){t=j;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var w=y.memoizedProps,x=y.memoizedState,v=t.stateNode,p=v.getSnapshotBeforeUpdate(t.elementType===t.type?w:at(t.type,w),x);v.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(_){ee(t,t.return,_)}if(e=t.sibling,e!==null){e.return=t.return,j=e;break}j=t.return}return y=ah,ah=!1,y}function Ti(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&eu(t,n,s)}i=i.next}while(i!==r)}}function Qo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function tu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function W0(e){var t=e.alternate;t!==null&&(e.alternate=null,W0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[yt],delete t[Ki],delete t[Bl],delete t[E1],delete t[b1])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function K0(e){return e.tag===5||e.tag===3||e.tag===4}function lh(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||K0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function nu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=co));else if(r!==4&&(e=e.child,e!==null))for(nu(e,t,n),e=e.sibling;e!==null;)nu(e,t,n),e=e.sibling}function ru(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ru(e,t,n),e=e.sibling;e!==null;)ru(e,t,n),e=e.sibling}var ge=null,lt=!1;function Wt(e,t,n){for(n=n.child;n!==null;)G0(e,t,n),n=n.sibling}function G0(e,t,n){if(wt&&typeof wt.onCommitFiberUnmount=="function")try{wt.onCommitFiberUnmount(Uo,n)}catch{}switch(n.tag){case 5:_e||wr(n,t);case 6:var r=ge,i=lt;ge=null,Wt(e,t,n),ge=r,lt=i,ge!==null&&(lt?(e=ge,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ge.removeChild(n.stateNode));break;case 18:ge!==null&&(lt?(e=ge,n=n.stateNode,e.nodeType===8?za(e.parentNode,n):e.nodeType===1&&za(e,n),Bi(e)):za(ge,n.stateNode));break;case 4:r=ge,i=lt,ge=n.stateNode.containerInfo,lt=!0,Wt(e,t,n),ge=r,lt=i;break;case 0:case 11:case 14:case 15:if(!_e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&eu(n,t,o),i=i.next}while(i!==r)}Wt(e,t,n);break;case 1:if(!_e&&(wr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ee(n,t,a)}Wt(e,t,n);break;case 21:Wt(e,t,n);break;case 22:n.mode&1?(_e=(r=_e)||n.memoizedState!==null,Wt(e,t,n),_e=r):Wt(e,t,n);break;default:Wt(e,t,n)}}function uh(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new U1),t.forEach(function(r){var i=X1.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function st(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:ge=a.stateNode,lt=!1;break e;case 3:ge=a.stateNode.containerInfo,lt=!0;break e;case 4:ge=a.stateNode.containerInfo,lt=!0;break e}a=a.return}if(ge===null)throw Error(T(160));G0(s,o,i),ge=null,lt=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){ee(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)q0(t,e),t=t.sibling}function q0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(st(t,e),mt(e),r&4){try{Ti(3,e,e.return),Qo(3,e)}catch(w){ee(e,e.return,w)}try{Ti(5,e,e.return)}catch(w){ee(e,e.return,w)}}break;case 1:st(t,e),mt(e),r&512&&n!==null&&wr(n,n.return);break;case 5:if(st(t,e),mt(e),r&512&&n!==null&&wr(n,n.return),e.flags&32){var i=e.stateNode;try{Di(i,"")}catch(w){ee(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&gp(i,s),Tl(a,o);var u=Tl(a,s);for(o=0;o<l.length;o+=2){var c=l[o],d=l[o+1];c==="style"?Sp(i,d):c==="dangerouslySetInnerHTML"?wp(i,d):c==="children"?Di(i,d):Uu(i,c,d,u)}switch(a){case"input":_l(i,s);break;case"textarea":vp(i,s);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var f=s.value;f!=null?br(i,!!s.multiple,f,!1):h!==!!s.multiple&&(s.defaultValue!=null?br(i,!!s.multiple,s.defaultValue,!0):br(i,!!s.multiple,s.multiple?[]:"",!1))}i[Ki]=s}catch(w){ee(e,e.return,w)}}break;case 6:if(st(t,e),mt(e),r&4){if(e.stateNode===null)throw Error(T(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(w){ee(e,e.return,w)}}break;case 3:if(st(t,e),mt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Bi(t.containerInfo)}catch(w){ee(e,e.return,w)}break;case 4:st(t,e),mt(e);break;case 13:st(t,e),mt(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Sc=re())),r&4&&uh(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(_e=(u=_e)||c,st(t,e),_e=u):st(t,e),mt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(j=e,c=e.child;c!==null;){for(d=j=c;j!==null;){switch(h=j,f=h.child,h.tag){case 0:case 11:case 14:case 15:Ti(4,h,h.return);break;case 1:wr(h,h.return);var y=h.stateNode;if(typeof y.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(w){ee(r,n,w)}}break;case 5:wr(h,h.return);break;case 22:if(h.memoizedState!==null){dh(d);continue}}f!==null?(f.return=h,j=f):dh(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{i=d.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=d.stateNode,l=d.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=xp("display",o))}catch(w){ee(e,e.return,w)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(w){ee(e,e.return,w)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:st(t,e),mt(e),r&4&&uh(e);break;case 21:break;default:st(t,e),mt(e)}}function mt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(K0(n)){var r=n;break e}n=n.return}throw Error(T(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Di(i,""),r.flags&=-33);var s=lh(e);ru(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,a=lh(e);nu(e,a,o);break;default:throw Error(T(161))}}catch(l){ee(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function H1(e,t,n){j=e,J0(e)}function J0(e,t,n){for(var r=(e.mode&1)!==0;j!==null;){var i=j,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||As;if(!o){var a=i.alternate,l=a!==null&&a.memoizedState!==null||_e;a=As;var u=_e;if(As=o,(_e=l)&&!u)for(j=i;j!==null;)o=j,l=o.child,o.tag===22&&o.memoizedState!==null?hh(i):l!==null?(l.return=o,j=l):hh(i);for(;s!==null;)j=s,J0(s),s=s.sibling;j=i,As=a,_e=u}ch(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,j=s):ch(e)}}function ch(e){for(;j!==null;){var t=j;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:_e||Qo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!_e)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:at(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&qd(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}qd(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Bi(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}_e||t.flags&512&&tu(t)}catch(h){ee(t,t.return,h)}}if(t===e){j=null;break}if(n=t.sibling,n!==null){n.return=t.return,j=n;break}j=t.return}}function dh(e){for(;j!==null;){var t=j;if(t===e){j=null;break}var n=t.sibling;if(n!==null){n.return=t.return,j=n;break}j=t.return}}function hh(e){for(;j!==null;){var t=j;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Qo(4,t)}catch(l){ee(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){ee(t,i,l)}}var s=t.return;try{tu(t)}catch(l){ee(t,s,l)}break;case 5:var o=t.return;try{tu(t)}catch(l){ee(t,o,l)}}}catch(l){ee(t,t.return,l)}if(t===e){j=null;break}var a=t.sibling;if(a!==null){a.return=t.return,j=a;break}j=t.return}}var W1=Math.ceil,_o=Bt.ReactCurrentDispatcher,wc=Bt.ReactCurrentOwner,tt=Bt.ReactCurrentBatchConfig,z=0,fe=null,oe=null,ve=0,Be=0,xr=Sn(0),le=0,Xi=null,Qn=0,Yo=0,xc=0,Pi=null,Oe=null,Sc=0,zr=1/0,bt=null,ko=!1,iu=null,un=null,Rs=!1,tn=null,Co=0,ji=0,su=null,Js=-1,Qs=0;function Te(){return z&6?re():Js!==-1?Js:Js=re()}function cn(e){return e.mode&1?z&2&&ve!==0?ve&-ve:P1.transition!==null?(Qs===0&&(Qs=Lp()),Qs):(e=B,e!==0||(e=window.event,e=e===void 0?16:zp(e.type)),e):1}function ht(e,t,n,r){if(50<ji)throw ji=0,su=null,Error(T(185));ss(e,n,r),(!(z&2)||e!==fe)&&(e===fe&&(!(z&2)&&(Yo|=n),le===4&&Xt(e,ve)),De(e,r),n===1&&z===0&&!(t.mode&1)&&(zr=re()+500,Go&&_n()))}function De(e,t){var n=e.callbackNode;Py(e,t);var r=oo(e,e===fe?ve:0);if(r===0)n!==null&&Sd(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Sd(n),t===1)e.tag===0?T1(fh.bind(null,e)):s0(fh.bind(null,e)),k1(function(){!(z&6)&&_n()}),n=null;else{switch(Ip(r)){case 1:n=Gu;break;case 4:n=Rp;break;case 16:n=so;break;case 536870912:n=Op;break;default:n=so}n=rm(n,Q0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Q0(e,t){if(Js=-1,Qs=0,z&6)throw Error(T(327));var n=e.callbackNode;if(Rr()&&e.callbackNode!==n)return null;var r=oo(e,e===fe?ve:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Eo(e,r);else{t=r;var i=z;z|=2;var s=X0();(fe!==e||ve!==t)&&(bt=null,zr=re()+500,Hn(e,t));do try{q1();break}catch(a){Y0(e,a)}while(1);oc(),_o.current=s,z=i,oe!==null?t=0:(fe=null,ve=0,t=le)}if(t!==0){if(t===2&&(i=Ol(e),i!==0&&(r=i,t=ou(e,i))),t===1)throw n=Xi,Hn(e,0),Xt(e,r),De(e,re()),n;if(t===6)Xt(e,r);else{if(i=e.current.alternate,!(r&30)&&!K1(i)&&(t=Eo(e,r),t===2&&(s=Ol(e),s!==0&&(r=s,t=ou(e,s))),t===1))throw n=Xi,Hn(e,0),Xt(e,r),De(e,re()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(T(345));case 2:Ln(e,Oe,bt);break;case 3:if(Xt(e,r),(r&130023424)===r&&(t=Sc+500-re(),10<t)){if(oo(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Te(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=zl(Ln.bind(null,e,Oe,bt),t);break}Ln(e,Oe,bt);break;case 4:if(Xt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-dt(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=re()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*W1(r/1960))-r,10<r){e.timeoutHandle=zl(Ln.bind(null,e,Oe,bt),r);break}Ln(e,Oe,bt);break;case 5:Ln(e,Oe,bt);break;default:throw Error(T(329))}}}return De(e,re()),e.callbackNode===n?Q0.bind(null,e):null}function ou(e,t){var n=Pi;return e.current.memoizedState.isDehydrated&&(Hn(e,t).flags|=256),e=Eo(e,t),e!==2&&(t=Oe,Oe=n,t!==null&&au(t)),e}function au(e){Oe===null?Oe=e:Oe.push.apply(Oe,e)}function K1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!ft(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Xt(e,t){for(t&=~xc,t&=~Yo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-dt(t),r=1<<n;e[n]=-1,t&=~r}}function fh(e){if(z&6)throw Error(T(327));Rr();var t=oo(e,0);if(!(t&1))return De(e,re()),null;var n=Eo(e,t);if(e.tag!==0&&n===2){var r=Ol(e);r!==0&&(t=r,n=ou(e,r))}if(n===1)throw n=Xi,Hn(e,0),Xt(e,t),De(e,re()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Ln(e,Oe,bt),De(e,re()),null}function _c(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(zr=re()+500,Go&&_n())}}function Yn(e){tn!==null&&tn.tag===0&&!(z&6)&&Rr();var t=z;z|=1;var n=tt.transition,r=B;try{if(tt.transition=null,B=1,e)return e()}finally{B=r,tt.transition=n,z=t,!(z&6)&&_n()}}function kc(){Be=xr.current,K(xr)}function Hn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,_1(n)),oe!==null)for(n=oe.return;n!==null;){var r=n;switch(rc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ho();break;case 3:$r(),K(Ne),K(ke),hc();break;case 5:dc(r);break;case 4:$r();break;case 13:K(Q);break;case 19:K(Q);break;case 10:ac(r.type._context);break;case 22:case 23:kc()}n=n.return}if(fe=e,oe=e=dn(e.current,null),ve=Be=t,le=0,Xi=null,xc=Yo=Qn=0,Oe=Pi=null,Bn!==null){for(t=0;t<Bn.length;t++)if(n=Bn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}Bn=null}return e}function Y0(e,t){do{var n=oe;try{if(oc(),Ks.current=So,xo){for(var r=X.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}xo=!1}if(Jn=0,he=ae=X=null,bi=!1,Ji=0,wc.current=null,n===null||n.return===null){le=1,Xi=t,oe=null;break}e:{var s=e,o=n.return,a=n,l=t;if(t=ve,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var h=c.alternate;h?(c.updateQueue=h.updateQueue,c.memoizedState=h.memoizedState,c.lanes=h.lanes):(c.updateQueue=null,c.memoizedState=null)}var f=eh(o);if(f!==null){f.flags&=-257,th(f,o,a,s,t),f.mode&1&&Zd(s,u,t),t=f,l=u;var y=t.updateQueue;if(y===null){var w=new Set;w.add(l),t.updateQueue=w}else y.add(l);break e}else{if(!(t&1)){Zd(s,u,t),Cc();break e}l=Error(T(426))}}else if(q&&a.mode&1){var x=eh(o);if(x!==null){!(x.flags&65536)&&(x.flags|=256),th(x,o,a,s,t),ic(Vr(l,a));break e}}s=l=Vr(l,a),le!==4&&(le=2),Pi===null?Pi=[s]:Pi.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var v=I0(s,l,t);Gd(s,v);break e;case 1:a=l;var p=s.type,m=s.stateNode;if(!(s.flags&128)&&(typeof p.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(un===null||!un.has(m)))){s.flags|=65536,t&=-t,s.lanes|=t;var _=N0(s,a,t);Gd(s,_);break e}}s=s.return}while(s!==null)}em(n)}catch(k){t=k,oe===n&&n!==null&&(oe=n=n.return);continue}break}while(1)}function X0(){var e=_o.current;return _o.current=So,e===null?So:e}function Cc(){(le===0||le===3||le===2)&&(le=4),fe===null||!(Qn&268435455)&&!(Yo&268435455)||Xt(fe,ve)}function Eo(e,t){var n=z;z|=2;var r=X0();(fe!==e||ve!==t)&&(bt=null,Hn(e,t));do try{G1();break}catch(i){Y0(e,i)}while(1);if(oc(),z=n,_o.current=r,oe!==null)throw Error(T(261));return fe=null,ve=0,le}function G1(){for(;oe!==null;)Z0(oe)}function q1(){for(;oe!==null&&!wy();)Z0(oe)}function Z0(e){var t=nm(e.alternate,e,Be);e.memoizedProps=e.pendingProps,t===null?em(e):oe=t,wc.current=null}function em(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=B1(n,t),n!==null){n.flags&=32767,oe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{le=6,oe=null;return}}else if(n=z1(n,t,Be),n!==null){oe=n;return}if(t=t.sibling,t!==null){oe=t;return}oe=t=e}while(t!==null);le===0&&(le=5)}function Ln(e,t,n){var r=B,i=tt.transition;try{tt.transition=null,B=1,J1(e,t,n,r)}finally{tt.transition=i,B=r}return null}function J1(e,t,n,r){do Rr();while(tn!==null);if(z&6)throw Error(T(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(jy(e,s),e===fe&&(oe=fe=null,ve=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Rs||(Rs=!0,rm(so,function(){return Rr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=tt.transition,tt.transition=null;var o=B;B=1;var a=z;z|=4,wc.current=null,F1(e,n),q0(n,e),m1($l),ao=!!Dl,$l=Dl=null,e.current=n,H1(n),xy(),z=a,B=o,tt.transition=s}else e.current=n;if(Rs&&(Rs=!1,tn=e,Co=i),s=e.pendingLanes,s===0&&(un=null),ky(n.stateNode),De(e,re()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(ko)throw ko=!1,e=iu,iu=null,e;return Co&1&&e.tag!==0&&Rr(),s=e.pendingLanes,s&1?e===su?ji++:(ji=0,su=e):ji=0,_n(),null}function Rr(){if(tn!==null){var e=Ip(Co),t=tt.transition,n=B;try{if(tt.transition=null,B=16>e?16:e,tn===null)var r=!1;else{if(e=tn,tn=null,Co=0,z&6)throw Error(T(331));var i=z;for(z|=4,j=e.current;j!==null;){var s=j,o=s.child;if(j.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(j=u;j!==null;){var c=j;switch(c.tag){case 0:case 11:case 15:Ti(8,c,s)}var d=c.child;if(d!==null)d.return=c,j=d;else for(;j!==null;){c=j;var h=c.sibling,f=c.return;if(W0(c),c===u){j=null;break}if(h!==null){h.return=f,j=h;break}j=f}}}var y=s.alternate;if(y!==null){var w=y.child;if(w!==null){y.child=null;do{var x=w.sibling;w.sibling=null,w=x}while(w!==null)}}j=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,j=o;else e:for(;j!==null;){if(s=j,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Ti(9,s,s.return)}var v=s.sibling;if(v!==null){v.return=s.return,j=v;break e}j=s.return}}var p=e.current;for(j=p;j!==null;){o=j;var m=o.child;if(o.subtreeFlags&2064&&m!==null)m.return=o,j=m;else e:for(o=p;j!==null;){if(a=j,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Qo(9,a)}}catch(k){ee(a,a.return,k)}if(a===o){j=null;break e}var _=a.sibling;if(_!==null){_.return=a.return,j=_;break e}j=a.return}}if(z=i,_n(),wt&&typeof wt.onPostCommitFiberRoot=="function")try{wt.onPostCommitFiberRoot(Uo,e)}catch{}r=!0}return r}finally{B=n,tt.transition=t}}return!1}function ph(e,t,n){t=Vr(n,t),t=I0(e,t,1),e=ln(e,t,1),t=Te(),e!==null&&(ss(e,1,t),De(e,t))}function ee(e,t,n){if(e.tag===3)ph(e,e,n);else for(;t!==null;){if(t.tag===3){ph(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(un===null||!un.has(r))){e=Vr(n,e),e=N0(t,e,1),t=ln(t,e,1),e=Te(),t!==null&&(ss(t,1,e),De(t,e));break}}t=t.return}}function Q1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Te(),e.pingedLanes|=e.suspendedLanes&n,fe===e&&(ve&n)===n&&(le===4||le===3&&(ve&130023424)===ve&&500>re()-Sc?Hn(e,0):xc|=n),De(e,t)}function tm(e,t){t===0&&(e.mode&1?(t=Ss,Ss<<=1,!(Ss&130023424)&&(Ss=4194304)):t=1);var n=Te();e=Dt(e,t),e!==null&&(ss(e,t,n),De(e,n))}function Y1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),tm(e,n)}function X1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(T(314))}r!==null&&r.delete(t),tm(e,n)}var nm;nm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ne.current)Ie=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ie=!1,V1(e,t,n);Ie=!!(e.flags&131072)}else Ie=!1,q&&t.flags&1048576&&o0(t,mo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;qs(e,t),e=t.pendingProps;var i=Nr(t,ke.current);Ar(t,n),i=pc(null,t,r,e,i,n);var s=mc();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Me(r)?(s=!0,fo(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,uc(t),i.updater=Jo,t.stateNode=i,i._reactInternals=t,Gl(t,r,e,n),t=Ql(null,t,r,!0,s,n)):(t.tag=0,q&&s&&nc(t),be(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(qs(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=ew(r),e=at(r,e),i){case 0:t=Jl(null,t,r,e,n);break e;case 1:t=ih(null,t,r,e,n);break e;case 11:t=nh(null,t,r,e,n);break e;case 14:t=rh(null,t,r,at(r.type,e),n);break e}throw Error(T(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:at(r,i),Jl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:at(r,i),ih(e,t,r,i,n);case 3:e:{if(V0(t),e===null)throw Error(T(387));r=t.pendingProps,s=t.memoizedState,i=s.element,h0(e,t),yo(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=Vr(Error(T(423)),t),t=sh(e,t,r,n,i);break e}else if(r!==i){i=Vr(Error(T(424)),t),t=sh(e,t,r,n,i);break e}else for(Ue=an(t.stateNode.containerInfo.firstChild),He=t,q=!0,ct=null,n=c0(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Mr(),r===i){t=$t(e,t,n);break e}be(e,t,r,n)}t=t.child}return t;case 5:return f0(t),e===null&&Hl(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,Vl(r,i)?o=null:s!==null&&Vl(r,s)&&(t.flags|=32),$0(e,t),be(e,t,o,n),t.child;case 6:return e===null&&Hl(t),null;case 13:return z0(e,t,n);case 4:return cc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Dr(t,null,r,n):be(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:at(r,i),nh(e,t,r,i,n);case 7:return be(e,t,t.pendingProps,n),t.child;case 8:return be(e,t,t.pendingProps.children,n),t.child;case 12:return be(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,F(go,r._currentValue),r._currentValue=o,s!==null)if(ft(s.value,o)){if(s.children===i.children&&!Ne.current){t=$t(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){o=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=Ot(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Wl(s.return,n,t),a.lanes|=n;break}l=l.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(T(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Wl(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}be(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Ar(t,n),i=rt(i),r=r(i),t.flags|=1,be(e,t,r,n),t.child;case 14:return r=t.type,i=at(r,t.pendingProps),i=at(r.type,i),rh(e,t,r,i,n);case 15:return M0(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:at(r,i),qs(e,t),t.tag=1,Me(r)?(e=!0,fo(t)):e=!1,Ar(t,n),L0(t,r,i),Gl(t,r,i,n),Ql(null,t,r,!0,e,n);case 19:return B0(e,t,n);case 22:return D0(e,t,n)}throw Error(T(156,t.tag))};function rm(e,t){return Ap(e,t)}function Z1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function et(e,t,n,r){return new Z1(e,t,n,r)}function Ec(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ew(e){if(typeof e=="function")return Ec(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Hu)return 11;if(e===Wu)return 14}return 2}function dn(e,t){var n=e.alternate;return n===null?(n=et(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ys(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")Ec(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case cr:return Wn(n.children,i,s,t);case Fu:o=8,i|=8;break;case vl:return e=et(12,n,t,i|2),e.elementType=vl,e.lanes=s,e;case yl:return e=et(13,n,t,i),e.elementType=yl,e.lanes=s,e;case wl:return e=et(19,n,t,i),e.elementType=wl,e.lanes=s,e;case fp:return Xo(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case dp:o=10;break e;case hp:o=9;break e;case Hu:o=11;break e;case Wu:o=14;break e;case Gt:o=16,r=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=et(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function Wn(e,t,n,r){return e=et(7,e,r,t),e.lanes=n,e}function Xo(e,t,n,r){return e=et(22,e,r,t),e.elementType=fp,e.lanes=n,e.stateNode={isHidden:!1},e}function qa(e,t,n){return e=et(6,e,null,t),e.lanes=n,e}function Ja(e,t,n){return t=et(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function tw(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ja(0),this.expirationTimes=ja(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ja(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function bc(e,t,n,r,i,s,o,a,l){return e=new tw(e,t,n,a,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=et(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},uc(s),e}function nw(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ur,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function im(e){if(!e)return vn;e=e._reactInternals;e:{if(Zn(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Me(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(Me(n))return i0(e,n,t)}return t}function sm(e,t,n,r,i,s,o,a,l){return e=bc(n,r,!0,e,i,s,o,a,l),e.context=im(null),n=e.current,r=Te(),i=cn(n),s=Ot(r,i),s.callback=t??null,ln(n,s,i),e.current.lanes=i,ss(e,i,r),De(e,r),e}function Zo(e,t,n,r){var i=t.current,s=Te(),o=cn(i);return n=im(n),t.context===null?t.context=n:t.pendingContext=n,t=Ot(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=ln(i,t,o),e!==null&&(ht(e,i,o,s),Ws(e,i,o)),o}function bo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function mh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Tc(e,t){mh(e,t),(e=e.alternate)&&mh(e,t)}function rw(){return null}var om=typeof reportError=="function"?reportError:function(e){console.error(e)};function Pc(e){this._internalRoot=e}ea.prototype.render=Pc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));Zo(e,t,null,null)};ea.prototype.unmount=Pc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Yn(function(){Zo(null,e,null,null)}),t[Mt]=null}};function ea(e){this._internalRoot=e}ea.prototype.unstable_scheduleHydration=function(e){if(e){var t=Dp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Yt.length&&t!==0&&t<Yt[n].priority;n++);Yt.splice(n,0,e),n===0&&Vp(e)}};function jc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ta(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function gh(){}function iw(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=bo(o);s.call(u)}}var o=sm(t,r,e,0,null,!1,!1,"",gh);return e._reactRootContainer=o,e[Mt]=o.current,Hi(e.nodeType===8?e.parentNode:e),Yn(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=bo(l);a.call(u)}}var l=bc(e,0,!1,null,null,!1,!1,"",gh);return e._reactRootContainer=l,e[Mt]=l.current,Hi(e.nodeType===8?e.parentNode:e),Yn(function(){Zo(t,l,n,r)}),l}function na(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var a=i;i=function(){var l=bo(o);a.call(l)}}Zo(t,o,e,i)}else o=iw(n,t,e,i,r);return bo(o)}Np=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fi(t.pendingLanes);n!==0&&(qu(t,n|1),De(t,re()),!(z&6)&&(zr=re()+500,_n()))}break;case 13:Yn(function(){var r=Dt(e,1);if(r!==null){var i=Te();ht(r,e,1,i)}}),Tc(e,1)}};Ju=function(e){if(e.tag===13){var t=Dt(e,134217728);if(t!==null){var n=Te();ht(t,e,134217728,n)}Tc(e,134217728)}};Mp=function(e){if(e.tag===13){var t=cn(e),n=Dt(e,t);if(n!==null){var r=Te();ht(n,e,t,r)}Tc(e,t)}};Dp=function(){return B};$p=function(e,t){var n=B;try{return B=e,t()}finally{B=n}};jl=function(e,t,n){switch(t){case"input":if(_l(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Ko(r);if(!i)throw Error(T(90));mp(r),_l(r,i)}}}break;case"textarea":vp(e,n);break;case"select":t=n.value,t!=null&&br(e,!!n.multiple,t,!1)}};Cp=_c;Ep=Yn;var sw={usingClientEntryPoint:!1,Events:[as,pr,Ko,_p,kp,_c]},ai={findFiberByHostInstance:zn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ow={bundleType:ai.bundleType,version:ai.version,rendererPackageName:ai.rendererPackageName,rendererConfig:ai.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Bt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Pp(e),e===null?null:e.stateNode},findFiberByHostInstance:ai.findFiberByHostInstance||rw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Os=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Os.isDisabled&&Os.supportsFiber)try{Uo=Os.inject(ow),wt=Os}catch{}}Ge.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=sw;Ge.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!jc(t))throw Error(T(200));return nw(e,t,null,n)};Ge.createRoot=function(e,t){if(!jc(e))throw Error(T(299));var n=!1,r="",i=om;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=bc(e,1,!1,null,null,n,!1,r,i),e[Mt]=t.current,Hi(e.nodeType===8?e.parentNode:e),new Pc(t)};Ge.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=Pp(t),e=e===null?null:e.stateNode,e};Ge.flushSync=function(e){return Yn(e)};Ge.hydrate=function(e,t,n){if(!ta(t))throw Error(T(200));return na(null,e,t,!0,n)};Ge.hydrateRoot=function(e,t,n){if(!jc(e))throw Error(T(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=om;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=sm(t,null,e,1,n??null,i,!1,s,o),e[Mt]=t.current,Hi(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new ea(t)};Ge.render=function(e,t,n){if(!ta(t))throw Error(T(200));return na(null,e,t,!1,n)};Ge.unmountComponentAtNode=function(e){if(!ta(e))throw Error(T(40));return e._reactRootContainer?(Yn(function(){na(null,null,e,!1,function(){e._reactRootContainer=null,e[Mt]=null})}),!0):!1};Ge.unstable_batchedUpdates=_c;Ge.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ta(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return na(e,t,n,!1,r)};Ge.version="18.3.1-next-f1338f8080-20240426";function am(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(am)}catch(e){console.error(e)}}am(),ap.exports=Ge;var aw=ap.exports,vh=aw;ml.createRoot=vh.createRoot,ml.hydrateRoot=vh.hydrateRoot;const lw="modulepreload",uw=function(e){return"/"+e},yh={},Ve=function(t,n,r){if(!n||n.length===0)return t();const i=document.getElementsByTagName("link");return Promise.all(n.map(s=>{if(s=uw(s),s in yh)return;yh[s]=!0;const o=s.endsWith(".css"),a=o?'[rel="stylesheet"]':"";if(!!r)for(let c=i.length-1;c>=0;c--){const d=i[c];if(d.href===s&&(!o||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${a}`))return;const u=document.createElement("link");if(u.rel=o?"stylesheet":lw,o||(u.as="script",u.crossOrigin=""),u.href=s,document.head.appendChild(u),o)return new Promise((c,d)=>{u.addEventListener("load",c),u.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${s}`)))})})).then(()=>t()).catch(s=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=s,window.dispatchEvent(o),!o.defaultPrevented)throw s})};/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Zi(){return Zi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Zi.apply(this,arguments)}var nn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(nn||(nn={}));const wh="popstate";function cw(e){e===void 0&&(e={});function t(r,i){let{pathname:s,search:o,hash:a}=r.location;return lu("",{pathname:s,search:o,hash:a},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:To(i)}return hw(t,n,null,e)}function ie(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function lm(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function dw(){return Math.random().toString(36).substr(2,8)}function xh(e,t){return{usr:e.state,key:e.key,idx:t}}function lu(e,t,n,r){return n===void 0&&(n=null),Zi({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Jr(t):t,{state:n,key:t&&t.key||r||dw()})}function To(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Jr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function hw(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,o=i.history,a=nn.Pop,l=null,u=c();u==null&&(u=0,o.replaceState(Zi({},o.state,{idx:u}),""));function c(){return(o.state||{idx:null}).idx}function d(){a=nn.Pop;let x=c(),v=x==null?null:x-u;u=x,l&&l({action:a,location:w.location,delta:v})}function h(x,v){a=nn.Push;let p=lu(w.location,x,v);n&&n(p,x),u=c()+1;let m=xh(p,u),_=w.createHref(p);try{o.pushState(m,"",_)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;i.location.assign(_)}s&&l&&l({action:a,location:w.location,delta:1})}function f(x,v){a=nn.Replace;let p=lu(w.location,x,v);n&&n(p,x),u=c();let m=xh(p,u),_=w.createHref(p);o.replaceState(m,"",_),s&&l&&l({action:a,location:w.location,delta:0})}function y(x){let v=i.location.origin!=="null"?i.location.origin:i.location.href,p=typeof x=="string"?x:To(x);return p=p.replace(/ $/,"%20"),ie(v,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,v)}let w={get action(){return a},get location(){return e(i,o)},listen(x){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(wh,d),l=x,()=>{i.removeEventListener(wh,d),l=null}},createHref(x){return t(i,x)},createURL:y,encodeLocation(x){let v=y(x);return{pathname:v.pathname,search:v.search,hash:v.hash}},push:h,replace:f,go(x){return o.go(x)}};return w}var Sh;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Sh||(Sh={}));function fw(e,t,n){return n===void 0&&(n="/"),pw(e,t,n,!1)}function pw(e,t,n,r){let i=typeof t=="string"?Jr(t):t,s=Ac(i.pathname||"/",n);if(s==null)return null;let o=um(e);mw(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let u=bw(s);a=Cw(o[l],u,r)}return a}function um(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,o,a)=>{let l={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};l.relativePath.startsWith("/")&&(ie(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=hn([r,l.relativePath]),c=n.concat(l);s.children&&s.children.length>0&&(ie(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),um(s.children,t,c,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:_w(u,s.index),routesMeta:c})};return e.forEach((s,o)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))i(s,o);else for(let l of cm(s.path))i(s,o,l)}),t}function cm(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let o=cm(r.join("/")),a=[];return a.push(...o.map(l=>l===""?s:[s,l].join("/"))),i&&a.push(...o),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function mw(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:kw(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const gw=/^:[\w-]+$/,vw=3,yw=2,ww=1,xw=10,Sw=-2,_h=e=>e==="*";function _w(e,t){let n=e.split("/"),r=n.length;return n.some(_h)&&(r+=Sw),t&&(r+=yw),n.filter(i=>!_h(i)).reduce((i,s)=>i+(gw.test(s)?vw:s===""?ww:xw),r)}function kw(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function Cw(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,i={},s="/",o=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,c=s==="/"?t:t.slice(s.length)||"/",d=kh({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),h=l.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=kh({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},c)),!d)return null;Object.assign(i,d.params),o.push({params:i,pathname:hn([s,d.pathname]),pathnameBase:Aw(hn([s,d.pathnameBase])),route:h}),d.pathnameBase!=="/"&&(s=hn([s,d.pathnameBase]))}return o}function kh(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Ew(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let s=i[0],o=s.replace(/(.)\/+$/,"$1"),a=i.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:h,isOptional:f}=c;if(h==="*"){let w=a[d]||"";o=s.slice(0,s.length-w.length).replace(/(.)\/+$/,"$1")}const y=a[d];return f&&!y?u[h]=void 0:u[h]=(y||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:o,pattern:e}}function Ew(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),lm(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function bw(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return lm(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Ac(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Tw(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?Jr(e):e;return{pathname:n?n.startsWith("/")?n:Pw(n,t):t,search:Rw(r),hash:Ow(i)}}function Pw(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Qa(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function jw(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Rc(e,t){let n=jw(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Oc(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=Jr(e):(i=Zi({},e),ie(!i.pathname||!i.pathname.includes("?"),Qa("?","pathname","search",i)),ie(!i.pathname||!i.pathname.includes("#"),Qa("#","pathname","hash",i)),ie(!i.search||!i.search.includes("#"),Qa("#","search","hash",i)));let s=e===""||i.pathname==="",o=s?"/":i.pathname,a;if(o==null)a=n;else{let d=t.length-1;if(!r&&o.startsWith("..")){let h=o.split("/");for(;h[0]==="..";)h.shift(),d-=1;i.pathname=h.join("/")}a=d>=0?t[d]:"/"}let l=Tw(i,a),u=o&&o!=="/"&&o.endsWith("/"),c=(s||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const hn=e=>e.join("/").replace(/\/\/+/g,"/"),Aw=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Rw=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ow=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Lw(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const dm=["post","put","patch","delete"];new Set(dm);const Iw=["get",...dm];new Set(Iw);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function es(){return es=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},es.apply(this,arguments)}const Lc=S.createContext(null),Nw=S.createContext(null),kn=S.createContext(null),ra=S.createContext(null),Ut=S.createContext({outlet:null,matches:[],isDataRoute:!1}),hm=S.createContext(null);function Mw(e,t){let{relative:n}=t===void 0?{}:t;Qr()||ie(!1);let{basename:r,navigator:i}=S.useContext(kn),{hash:s,pathname:o,search:a}=pm(e,{relative:n}),l=o;return r!=="/"&&(l=o==="/"?r:hn([r,o])),i.createHref({pathname:l,search:a,hash:s})}function Qr(){return S.useContext(ra)!=null}function Cn(){return Qr()||ie(!1),S.useContext(ra).location}function fm(e){S.useContext(kn).static||S.useLayoutEffect(e)}function En(){let{isDataRoute:e}=S.useContext(Ut);return e?Jw():Dw()}function Dw(){Qr()||ie(!1);let e=S.useContext(Lc),{basename:t,future:n,navigator:r}=S.useContext(kn),{matches:i}=S.useContext(Ut),{pathname:s}=Cn(),o=JSON.stringify(Rc(i,n.v7_relativeSplatPath)),a=S.useRef(!1);return fm(()=>{a.current=!0}),S.useCallback(function(u,c){if(c===void 0&&(c={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let d=Oc(u,JSON.parse(o),s,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:hn([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,o,s,e])}function tT(){let{matches:e}=S.useContext(Ut),t=e[e.length-1];return t?t.params:{}}function pm(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=S.useContext(kn),{matches:i}=S.useContext(Ut),{pathname:s}=Cn(),o=JSON.stringify(Rc(i,r.v7_relativeSplatPath));return S.useMemo(()=>Oc(e,JSON.parse(o),s,n==="path"),[e,o,s,n])}function $w(e,t){return Vw(e,t)}function Vw(e,t,n,r){Qr()||ie(!1);let{navigator:i}=S.useContext(kn),{matches:s}=S.useContext(Ut),o=s[s.length-1],a=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let u=Cn(),c;if(t){var d;let x=typeof t=="string"?Jr(t):t;l==="/"||(d=x.pathname)!=null&&d.startsWith(l)||ie(!1),c=x}else c=u;let h=c.pathname||"/",f=h;if(l!=="/"){let x=l.replace(/^\//,"").split("/");f="/"+h.replace(/^\//,"").split("/").slice(x.length).join("/")}let y=fw(e,{pathname:f}),w=Hw(y&&y.map(x=>Object.assign({},x,{params:Object.assign({},a,x.params),pathname:hn([l,i.encodeLocation?i.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?l:hn([l,i.encodeLocation?i.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),s,n,r);return t&&w?S.createElement(ra.Provider,{value:{location:es({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:nn.Pop}},w):w}function zw(){let e=qw(),t=Lw(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},s=null;return S.createElement(S.Fragment,null,S.createElement("h2",null,"Unexpected Application Error!"),S.createElement("h3",{style:{fontStyle:"italic"}},t),n?S.createElement("pre",{style:i},n):null,s)}const Bw=S.createElement(zw,null);class Uw extends S.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?S.createElement(Ut.Provider,{value:this.props.routeContext},S.createElement(hm.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Fw(e){let{routeContext:t,match:n,children:r}=e,i=S.useContext(Lc);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),S.createElement(Ut.Provider,{value:t},r)}function Hw(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,a=(i=n)==null?void 0:i.errors;if(a!=null){let c=o.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id])!==void 0);c>=0||ie(!1),o=o.slice(0,Math.min(o.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<o.length;c++){let d=o[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:h,errors:f}=n,y=d.route.loader&&h[d.route.id]===void 0&&(!f||f[d.route.id]===void 0);if(d.route.lazy||y){l=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((c,d,h)=>{let f,y=!1,w=null,x=null;n&&(f=a&&d.route.id?a[d.route.id]:void 0,w=d.route.errorElement||Bw,l&&(u<0&&h===0?(Qw("route-fallback",!1),y=!0,x=null):u===h&&(y=!0,x=d.route.hydrateFallbackElement||null)));let v=t.concat(o.slice(0,h+1)),p=()=>{let m;return f?m=w:y?m=x:d.route.Component?m=S.createElement(d.route.Component,null):d.route.element?m=d.route.element:m=c,S.createElement(Fw,{match:d,routeContext:{outlet:c,matches:v,isDataRoute:n!=null},children:m})};return n&&(d.route.ErrorBoundary||d.route.errorElement||h===0)?S.createElement(Uw,{location:n.location,revalidation:n.revalidation,component:w,error:f,children:p(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):p()},null)}var mm=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(mm||{}),Po=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Po||{});function Ww(e){let t=S.useContext(Lc);return t||ie(!1),t}function Kw(e){let t=S.useContext(Nw);return t||ie(!1),t}function Gw(e){let t=S.useContext(Ut);return t||ie(!1),t}function gm(e){let t=Gw(),n=t.matches[t.matches.length-1];return n.route.id||ie(!1),n.route.id}function qw(){var e;let t=S.useContext(hm),n=Kw(Po.UseRouteError),r=gm(Po.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Jw(){let{router:e}=Ww(mm.UseNavigateStable),t=gm(Po.UseNavigateStable),n=S.useRef(!1);return fm(()=>{n.current=!0}),S.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,es({fromRouteId:t},s)))},[e,t])}const Ch={};function Qw(e,t,n){!t&&!Ch[e]&&(Ch[e]=!0)}function Yw(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function uu(e){let{to:t,replace:n,state:r,relative:i}=e;Qr()||ie(!1);let{future:s,static:o}=S.useContext(kn),{matches:a}=S.useContext(Ut),{pathname:l}=Cn(),u=En(),c=Oc(t,Rc(a,s.v7_relativeSplatPath),l,i==="path"),d=JSON.stringify(c);return S.useEffect(()=>u(JSON.parse(d),{replace:n,state:r,relative:i}),[u,d,i,n,r]),null}function Re(e){ie(!1)}function Xw(e){let{basename:t="/",children:n=null,location:r,navigationType:i=nn.Pop,navigator:s,static:o=!1,future:a}=e;Qr()&&ie(!1);let l=t.replace(/^\/*/,"/"),u=S.useMemo(()=>({basename:l,navigator:s,static:o,future:es({v7_relativeSplatPath:!1},a)}),[l,a,s,o]);typeof r=="string"&&(r=Jr(r));let{pathname:c="/",search:d="",hash:h="",state:f=null,key:y="default"}=r,w=S.useMemo(()=>{let x=Ac(c,l);return x==null?null:{location:{pathname:x,search:d,hash:h,state:f,key:y},navigationType:i}},[l,c,d,h,f,y,i]);return w==null?null:S.createElement(kn.Provider,{value:u},S.createElement(ra.Provider,{children:n,value:w}))}function Zw(e){let{children:t,location:n}=e;return $w(cu(t),n)}new Promise(()=>{});function cu(e,t){t===void 0&&(t=[]);let n=[];return S.Children.forEach(e,(r,i)=>{if(!S.isValidElement(r))return;let s=[...t,i];if(r.type===S.Fragment){n.push.apply(n,cu(r.props.children,s));return}r.type!==Re&&ie(!1),!r.props.index||!r.props.children||ie(!1);let o={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=cu(r.props.children,s)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function du(){return du=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},du.apply(this,arguments)}function ex(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,s;for(s=0;s<r.length;s++)i=r[s],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function tx(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function nx(e,t){return e.button===0&&(!t||t==="_self")&&!tx(e)}const rx=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],ix="6";try{window.__reactRouterVersion=ix}catch{}const sx="startTransition",Eh=Qv[sx];function ox(e){let{basename:t,children:n,future:r,window:i}=e,s=S.useRef();s.current==null&&(s.current=cw({window:i,v5Compat:!0}));let o=s.current,[a,l]=S.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},c=S.useCallback(d=>{u&&Eh?Eh(()=>l(d)):l(d)},[l,u]);return S.useLayoutEffect(()=>o.listen(c),[o,c]),S.useEffect(()=>Yw(r),[r]),S.createElement(Xw,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:o,future:r})}const ax=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",lx=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,vm=S.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:s,replace:o,state:a,target:l,to:u,preventScrollReset:c,viewTransition:d}=t,h=ex(t,rx),{basename:f}=S.useContext(kn),y,w=!1;if(typeof u=="string"&&lx.test(u)&&(y=u,ax))try{let m=new URL(window.location.href),_=u.startsWith("//")?new URL(m.protocol+u):new URL(u),k=Ac(_.pathname,f);_.origin===m.origin&&k!=null?u=k+_.search+_.hash:w=!0}catch{}let x=Mw(u,{relative:i}),v=ux(u,{replace:o,state:a,target:l,preventScrollReset:c,relative:i,viewTransition:d});function p(m){r&&r(m),m.defaultPrevented||v(m)}return S.createElement("a",du({},h,{href:y||x,onClick:w||s?r:p,ref:n,target:l}))});var bh;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(bh||(bh={}));var Th;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Th||(Th={}));function ux(e,t){let{target:n,replace:r,state:i,preventScrollReset:s,relative:o,viewTransition:a}=t===void 0?{}:t,l=En(),u=Cn(),c=pm(e,{relative:o});return S.useCallback(d=>{if(nx(d,n)){d.preventDefault();let h=r!==void 0?r:To(u)===To(c);l(e,{replace:h,state:i,preventScrollReset:s,relative:o,viewTransition:a})}},[u,l,c,r,i,n,e,s,o,a])}const Ph=e=>{let t;const n=new Set,r=(c,d)=>{const h=typeof c=="function"?c(t):c;if(!Object.is(h,t)){const f=t;t=d??(typeof h!="object"||h===null)?h:Object.assign({},t,h),n.forEach(y=>y(t,f))}},i=()=>t,l={setState:r,getState:i,getInitialState:()=>u,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{n.clear()}},u=t=e(r,i,l);return l},cx=e=>e?Ph(e):Ph;var ym={exports:{}},wm={},xm={exports:{}},Sm={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Br=S;function dx(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var hx=typeof Object.is=="function"?Object.is:dx,fx=Br.useState,px=Br.useEffect,mx=Br.useLayoutEffect,gx=Br.useDebugValue;function vx(e,t){var n=t(),r=fx({inst:{value:n,getSnapshot:t}}),i=r[0].inst,s=r[1];return mx(function(){i.value=n,i.getSnapshot=t,Ya(i)&&s({inst:i})},[e,n,t]),px(function(){return Ya(i)&&s({inst:i}),e(function(){Ya(i)&&s({inst:i})})},[e]),gx(n),n}function Ya(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!hx(e,n)}catch{return!0}}function yx(e,t){return t()}var wx=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?yx:vx;Sm.useSyncExternalStore=Br.useSyncExternalStore!==void 0?Br.useSyncExternalStore:wx;xm.exports=Sm;var xx=xm.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ia=S,Sx=xx;function _x(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var kx=typeof Object.is=="function"?Object.is:_x,Cx=Sx.useSyncExternalStore,Ex=ia.useRef,bx=ia.useEffect,Tx=ia.useMemo,Px=ia.useDebugValue;wm.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var s=Ex(null);if(s.current===null){var o={hasValue:!1,value:null};s.current=o}else o=s.current;s=Tx(function(){function l(f){if(!u){if(u=!0,c=f,f=r(f),i!==void 0&&o.hasValue){var y=o.value;if(i(y,f))return d=y}return d=f}if(y=d,kx(c,f))return y;var w=r(f);return i!==void 0&&i(y,w)?(c=f,y):(c=f,d=w)}var u=!1,c,d,h=n===void 0?null:n;return[function(){return l(t())},h===null?void 0:function(){return l(h())}]},[t,n,r,i]);var a=Cx(e,s[0],s[1]);return bx(function(){o.hasValue=!0,o.value=a},[a]),Px(a),a};ym.exports=wm;var jx=ym.exports;const Ax=Jf(jx),{useDebugValue:Rx}=Fe,{useSyncExternalStoreWithSelector:Ox}=Ax;const Lx=e=>e;function Ix(e,t=Lx,n){const r=Ox(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return Rx(r),r}const jh=e=>{const t=typeof e=="function"?cx(e):e,n=(r,i)=>Ix(t,r,i);return Object.assign(n,t),n},_m=e=>e?jh(e):jh,Nx=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Ve(()=>Promise.resolve().then(()=>Yr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)};class sa extends Error{constructor(t,n="FunctionsError",r){super(t),this.name=n,this.context=r}}class km extends sa{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class Cm extends sa{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class Em extends sa{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var jo;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(jo||(jo={}));var Mx=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class Dx{constructor(t,{headers:n={},customFetch:r,region:i=jo.Any}={}){this.url=t,this.headers=n,this.region=i,this.fetch=Nx(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,n={}){var r;return Mx(this,void 0,void 0,function*(){try{const{headers:i,method:s,body:o}=n;let a={},{region:l}=n;l||(l=this.region),l&&l!=="any"&&(a["x-region"]=l);let u;o&&(i&&!Object.prototype.hasOwnProperty.call(i,"Content-Type")||!i)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",u=o):typeof o=="string"?(a["Content-Type"]="text/plain",u=o):typeof FormData<"u"&&o instanceof FormData?u=o:(a["Content-Type"]="application/json",u=JSON.stringify(o)));const c=yield this.fetch(`${this.url}/${t}`,{method:s||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),i),body:u}).catch(y=>{throw new km(y)}),d=c.headers.get("x-relay-error");if(d&&d==="true")throw new Cm(c);if(!c.ok)throw new Em(c);let h=((r=c.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),f;return h==="application/json"?f=yield c.json():h==="application/octet-stream"?f=yield c.blob():h==="text/event-stream"?f=c:h==="multipart/form-data"?f=yield c.formData():f=yield c.text(),{data:f,error:null}}catch(i){return{data:null,error:i}}})}}var Le={},Ic={},oa={},us={},aa={},la={},$x=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},Ur=$x();const Vx=Ur.fetch,bm=Ur.fetch.bind(Ur),Tm=Ur.Headers,zx=Ur.Request,Bx=Ur.Response,Yr=Object.freeze(Object.defineProperty({__proto__:null,Headers:Tm,Request:zx,Response:Bx,default:bm,fetch:Vx},Symbol.toStringTag,{value:"Module"})),Ux=Iv(Yr);var ua={};Object.defineProperty(ua,"__esModule",{value:!0});let Fx=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};ua.default=Fx;var Pm=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(la,"__esModule",{value:!0});const Hx=Pm(Ux),Wx=Pm(ua);let Kx=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=Hx.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,n){return this.headers=Object.assign({},this.headers),this.headers[t]=n,this}then(t,n){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let i=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async s=>{var o,a,l;let u=null,c=null,d=null,h=s.status,f=s.statusText;if(s.ok){if(this.method!=="HEAD"){const v=await s.text();v===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?c=v:c=JSON.parse(v))}const w=(o=this.headers.Prefer)===null||o===void 0?void 0:o.match(/count=(exact|planned|estimated)/),x=(a=s.headers.get("content-range"))===null||a===void 0?void 0:a.split("/");w&&x&&x.length>1&&(d=parseInt(x[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(c)&&(c.length>1?(u={code:"PGRST116",details:`Results contain ${c.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},c=null,d=null,h=406,f="Not Acceptable"):c.length===1?c=c[0]:c=null)}else{const w=await s.text();try{u=JSON.parse(w),Array.isArray(u)&&s.status===404&&(c=[],u=null,h=200,f="OK")}catch{s.status===404&&w===""?(h=204,f="No Content"):u={message:w}}if(u&&this.isMaybeSingle&&(!((l=u==null?void 0:u.details)===null||l===void 0)&&l.includes("0 rows"))&&(u=null,h=200,f="OK"),u&&this.shouldThrowOnError)throw new Wx.default(u)}return{error:u,data:c,count:d,status:h,statusText:f}});return this.shouldThrowOnError||(i=i.catch(s=>{var o,a,l;return{error:{message:`${(o=s==null?void 0:s.name)!==null&&o!==void 0?o:"FetchError"}: ${s==null?void 0:s.message}`,details:`${(a=s==null?void 0:s.stack)!==null&&a!==void 0?a:""}`,hint:"",code:`${(l=s==null?void 0:s.code)!==null&&l!==void 0?l:""}`},data:null,count:null,status:0,statusText:""}})),i.then(t,n)}returns(){return this}overrideTypes(){return this}};la.default=Kx;var Gx=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(aa,"__esModule",{value:!0});const qx=Gx(la);let Jx=class extends qx.default{select(t){let n=!1;const r=(t??"*").split("").map(i=>/\s/.test(i)&&!n?"":(i==='"'&&(n=!n),i)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:n=!0,nullsFirst:r,foreignTable:i,referencedTable:s=i}={}){const o=s?`${s}.order`:"order",a=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${a?`${a},`:""}${t}.${n?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:n,referencedTable:r=n}={}){const i=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${t}`),this}range(t,n,{foreignTable:r,referencedTable:i=r}={}){const s=typeof i>"u"?"offset":`${i}.offset`,o=typeof i>"u"?"limit":`${i}.limit`;return this.url.searchParams.set(s,`${t}`),this.url.searchParams.set(o,`${n-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:n=!1,settings:r=!1,buffers:i=!1,wal:s=!1,format:o="text"}={}){var a;const l=[t?"analyze":null,n?"verbose":null,r?"settings":null,i?"buffers":null,s?"wal":null].filter(Boolean).join("|"),u=(a=this.headers.Accept)!==null&&a!==void 0?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${u}"; options=${l};`,o==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};aa.default=Jx;var Qx=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(us,"__esModule",{value:!0});const Yx=Qx(aa);let Xx=class extends Yx.default{eq(t,n){return this.url.searchParams.append(t,`eq.${n}`),this}neq(t,n){return this.url.searchParams.append(t,`neq.${n}`),this}gt(t,n){return this.url.searchParams.append(t,`gt.${n}`),this}gte(t,n){return this.url.searchParams.append(t,`gte.${n}`),this}lt(t,n){return this.url.searchParams.append(t,`lt.${n}`),this}lte(t,n){return this.url.searchParams.append(t,`lte.${n}`),this}like(t,n){return this.url.searchParams.append(t,`like.${n}`),this}likeAllOf(t,n){return this.url.searchParams.append(t,`like(all).{${n.join(",")}}`),this}likeAnyOf(t,n){return this.url.searchParams.append(t,`like(any).{${n.join(",")}}`),this}ilike(t,n){return this.url.searchParams.append(t,`ilike.${n}`),this}ilikeAllOf(t,n){return this.url.searchParams.append(t,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(t,n){return this.url.searchParams.append(t,`ilike(any).{${n.join(",")}}`),this}is(t,n){return this.url.searchParams.append(t,`is.${n}`),this}in(t,n){const r=Array.from(new Set(n)).map(i=>typeof i=="string"&&new RegExp("[,()]").test(i)?`"${i}"`:`${i}`).join(",");return this.url.searchParams.append(t,`in.(${r})`),this}contains(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cs.{${n.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(n)}`),this}containedBy(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cd.{${n.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(n)}`),this}rangeGt(t,n){return this.url.searchParams.append(t,`sr.${n}`),this}rangeGte(t,n){return this.url.searchParams.append(t,`nxl.${n}`),this}rangeLt(t,n){return this.url.searchParams.append(t,`sl.${n}`),this}rangeLte(t,n){return this.url.searchParams.append(t,`nxr.${n}`),this}rangeAdjacent(t,n){return this.url.searchParams.append(t,`adj.${n}`),this}overlaps(t,n){return typeof n=="string"?this.url.searchParams.append(t,`ov.${n}`):this.url.searchParams.append(t,`ov.{${n.join(",")}}`),this}textSearch(t,n,{config:r,type:i}={}){let s="";i==="plain"?s="pl":i==="phrase"?s="ph":i==="websearch"&&(s="w");const o=r===void 0?"":`(${r})`;return this.url.searchParams.append(t,`${s}fts${o}.${n}`),this}match(t){return Object.entries(t).forEach(([n,r])=>{this.url.searchParams.append(n,`eq.${r}`)}),this}not(t,n,r){return this.url.searchParams.append(t,`not.${n}.${r}`),this}or(t,{foreignTable:n,referencedTable:r=n}={}){const i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${t})`),this}filter(t,n,r){return this.url.searchParams.append(t,`${n}.${r}`),this}};us.default=Xx;var Zx=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(oa,"__esModule",{value:!0});const li=Zx(us);let eS=class{constructor(t,{headers:n={},schema:r,fetch:i}){this.url=t,this.headers=n,this.schema=r,this.fetch=i}select(t,{head:n=!1,count:r}={}){const i=n?"HEAD":"GET";let s=!1;const o=(t??"*").split("").map(a=>/\s/.test(a)&&!s?"":(a==='"'&&(s=!s),a)).join("");return this.url.searchParams.set("select",o),r&&(this.headers.Prefer=`count=${r}`),new li.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:n,defaultToNull:r=!0}={}){const i="POST",s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),n&&s.push(`count=${n}`),r||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(t)){const o=t.reduce((a,l)=>a.concat(Object.keys(l)),[]);if(o.length>0){const a=[...new Set(o)].map(l=>`"${l}"`);this.url.searchParams.set("columns",a.join(","))}}return new li.default({method:i,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:n,ignoreDuplicates:r=!1,count:i,defaultToNull:s=!0}={}){const o="POST",a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(n!==void 0&&this.url.searchParams.set("on_conflict",n),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),s||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(t)){const l=t.reduce((u,c)=>u.concat(Object.keys(c)),[]);if(l.length>0){const u=[...new Set(l)].map(c=>`"${c}"`);this.url.searchParams.set("columns",u.join(","))}}return new li.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:n}={}){const r="PATCH",i=[];return this.headers.Prefer&&i.push(this.headers.Prefer),n&&i.push(`count=${n}`),this.headers.Prefer=i.join(","),new li.default({method:r,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const n="DELETE",r=[];return t&&r.push(`count=${t}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new li.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};oa.default=eS;var ca={},da={};Object.defineProperty(da,"__esModule",{value:!0});da.version=void 0;da.version="0.0.0-automated";Object.defineProperty(ca,"__esModule",{value:!0});ca.DEFAULT_HEADERS=void 0;const tS=da;ca.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${tS.version}`};var jm=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ic,"__esModule",{value:!0});const nS=jm(oa),rS=jm(us),iS=ca;let sS=class Am{constructor(t,{headers:n={},schema:r,fetch:i}={}){this.url=t,this.headers=Object.assign(Object.assign({},iS.DEFAULT_HEADERS),n),this.schemaName=r,this.fetch=i}from(t){const n=new URL(`${this.url}/${t}`);return new nS.default(n,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new Am(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,n={},{head:r=!1,get:i=!1,count:s}={}){let o;const a=new URL(`${this.url}/rpc/${t}`);let l;r||i?(o=r?"HEAD":"GET",Object.entries(n).filter(([c,d])=>d!==void 0).map(([c,d])=>[c,Array.isArray(d)?`{${d.join(",")}}`:`${d}`]).forEach(([c,d])=>{a.searchParams.append(c,d)})):(o="POST",l=n);const u=Object.assign({},this.headers);return s&&(u.Prefer=`count=${s}`),new rS.default({method:o,url:a,headers:u,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}};Ic.default=sS;var Xr=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Le,"__esModule",{value:!0});Le.PostgrestError=Le.PostgrestBuilder=Le.PostgrestTransformBuilder=Le.PostgrestFilterBuilder=Le.PostgrestQueryBuilder=Le.PostgrestClient=void 0;const Rm=Xr(Ic);Le.PostgrestClient=Rm.default;const Om=Xr(oa);Le.PostgrestQueryBuilder=Om.default;const Lm=Xr(us);Le.PostgrestFilterBuilder=Lm.default;const Im=Xr(aa);Le.PostgrestTransformBuilder=Im.default;const Nm=Xr(la);Le.PostgrestBuilder=Nm.default;const Mm=Xr(ua);Le.PostgrestError=Mm.default;var oS=Le.default={PostgrestClient:Rm.default,PostgrestQueryBuilder:Om.default,PostgrestFilterBuilder:Lm.default,PostgrestTransformBuilder:Im.default,PostgrestBuilder:Nm.default,PostgrestError:Mm.default};const{PostgrestClient:aS,PostgrestQueryBuilder:aT,PostgrestFilterBuilder:lT,PostgrestTransformBuilder:uT,PostgrestBuilder:cT,PostgrestError:lS}=oS;function uS(){if(typeof WebSocket<"u")return WebSocket;if(typeof global.WebSocket<"u")return global.WebSocket;if(typeof window.WebSocket<"u")return window.WebSocket;if(typeof self.WebSocket<"u")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const cS=uS(),dS="2.11.15",hS=`realtime-js/${dS}`,fS="1.0.0",Dm=1e4,pS=1e3;var Ai;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Ai||(Ai={}));var me;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(me||(me={}));var ut;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(ut||(ut={}));var hu;(function(e){e.websocket="websocket"})(hu||(hu={}));var Vn;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(Vn||(Vn={}));class mS{constructor(){this.HEADER_LENGTH=1}decode(t,n){return t.constructor===ArrayBuffer?n(this._binaryDecode(t)):n(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const n=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,n,r)}_decodeBroadcast(t,n,r){const i=n.getUint8(1),s=n.getUint8(2);let o=this.HEADER_LENGTH+2;const a=r.decode(t.slice(o,o+i));o=o+i;const l=r.decode(t.slice(o,o+s));o=o+s;const u=JSON.parse(r.decode(t.slice(o,t.byteLength)));return{ref:null,topic:a,event:l,payload:u}}}class $m{constructor(t,n){this.callback=t,this.timerCalc=n,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=n}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var U;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(U||(U={}));const Ah=(e,t,n={})=>{var r;const i=(r=n.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((s,o)=>(s[o]=gS(o,e,t,i),s),{})},gS=(e,t,n,r)=>{const i=t.find(a=>a.name===e),s=i==null?void 0:i.type,o=n[e];return s&&!r.includes(s)?Vm(s,o):fu(o)},Vm=(e,t)=>{if(e.charAt(0)==="_"){const n=e.slice(1,e.length);return xS(t,n)}switch(e){case U.bool:return vS(t);case U.float4:case U.float8:case U.int2:case U.int4:case U.int8:case U.numeric:case U.oid:return yS(t);case U.json:case U.jsonb:return wS(t);case U.timestamp:return SS(t);case U.abstime:case U.date:case U.daterange:case U.int4range:case U.int8range:case U.money:case U.reltime:case U.text:case U.time:case U.timestamptz:case U.timetz:case U.tsrange:case U.tstzrange:return fu(t);default:return fu(t)}},fu=e=>e,vS=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},yS=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},wS=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},xS=(e,t)=>{if(typeof e!="string")return e;const n=e.length-1,r=e[n];if(e[0]==="{"&&r==="}"){let s;const o=e.slice(1,n);try{s=JSON.parse("["+o+"]")}catch{s=o?o.split(","):[]}return s.map(a=>Vm(t,a))}return e},SS=e=>typeof e=="string"?e.replace(" ","T"):e,zm=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Xa{constructor(t,n,r={},i=Dm){this.channel=t,this.event=n,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,n){var r;return this._hasReceived(t)&&n((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:n}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=n=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=n,this._matchReceive(n)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,n){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:n})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:n}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(n))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var pu;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(pu||(pu={}));class Or{constructor(t,n){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(n==null?void 0:n.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},i=>{const{onJoin:s,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Or.syncState(this.state,i,s,o),this.pendingDiffs.forEach(l=>{this.state=Or.syncDiff(this.state,l,s,o)}),this.pendingDiffs=[],a()}),this.channel._on(r.diff,{},i=>{const{onJoin:s,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(i):(this.state=Or.syncDiff(this.state,i,s,o),a())}),this.onJoin((i,s,o)=>{this.channel._trigger("presence",{event:"join",key:i,currentPresences:s,newPresences:o})}),this.onLeave((i,s,o)=>{this.channel._trigger("presence",{event:"leave",key:i,currentPresences:s,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,n,r,i){const s=this.cloneDeep(t),o=this.transformState(n),a={},l={};return this.map(s,(u,c)=>{o[u]||(l[u]=c)}),this.map(o,(u,c)=>{const d=s[u];if(d){const h=c.map(x=>x.presence_ref),f=d.map(x=>x.presence_ref),y=c.filter(x=>f.indexOf(x.presence_ref)<0),w=d.filter(x=>h.indexOf(x.presence_ref)<0);y.length>0&&(a[u]=y),w.length>0&&(l[u]=w)}else a[u]=c}),this.syncDiff(s,{joins:a,leaves:l},r,i)}static syncDiff(t,n,r,i){const{joins:s,leaves:o}={joins:this.transformState(n.joins),leaves:this.transformState(n.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(s,(a,l)=>{var u;const c=(u=t[a])!==null&&u!==void 0?u:[];if(t[a]=this.cloneDeep(l),c.length>0){const d=t[a].map(f=>f.presence_ref),h=c.filter(f=>d.indexOf(f.presence_ref)<0);t[a].unshift(...h)}r(a,c,l)}),this.map(o,(a,l)=>{let u=t[a];if(!u)return;const c=l.map(d=>d.presence_ref);u=u.filter(d=>c.indexOf(d.presence_ref)<0),t[a]=u,i(a,u,l),u.length===0&&delete t[a]}),t}static map(t,n){return Object.getOwnPropertyNames(t).map(r=>n(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((n,r)=>{const i=t[r];return"metas"in i?n[r]=i.metas.map(s=>(s.presence_ref=s.phx_ref,delete s.phx_ref,delete s.phx_ref_prev,s)):n[r]=i,n},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var mu;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(mu||(mu={}));var gu;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(gu||(gu={}));var vt;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(vt||(vt={}));const _S=me;class ha{constructor(t,n={config:{}},r){this.topic=t,this.params=n,this.socket=r,this.bindings={},this.state=me.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},n.config),this.timeout=this.socket.timeout,this.joinPush=new Xa(this,ut.join,this.params,this.timeout),this.rejoinTimer=new $m(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=me.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(i=>i.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=me.closed,this.socket._remove(this)}),this._onError(i=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,i),this.state=me.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=me.errored,this.rejoinTimer.scheduleTimeout())}),this._on(ut.reply,{},(i,s)=>{this._trigger(this._replyEventName(s),i)}),this.presence=new Or(this),this.broadcastEndpointURL=zm(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,n=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.state==me.closed){const{config:{broadcast:s,presence:o,private:a}}=this.params;this._onError(c=>t==null?void 0:t(vt.CHANNEL_ERROR,c)),this._onClose(()=>t==null?void 0:t(vt.CLOSED));const l={},u={broadcast:s,presence:o,postgres_changes:(i=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(c=>c.filter))!==null&&i!==void 0?i:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},l)),this.joinedOnce=!0,this._rejoin(n),this.joinPush.receive("ok",async({postgres_changes:c})=>{var d;if(this.socket.setAuth(),c===void 0){t==null||t(vt.SUBSCRIBED);return}else{const h=this.bindings.postgres_changes,f=(d=h==null?void 0:h.length)!==null&&d!==void 0?d:0,y=[];for(let w=0;w<f;w++){const x=h[w],{filter:{event:v,schema:p,table:m,filter:_}}=x,k=c&&c[w];if(k&&k.event===v&&k.schema===p&&k.table===m&&k.filter===_)y.push(Object.assign(Object.assign({},x),{id:k.id}));else{this.unsubscribe(),this.state=me.errored,t==null||t(vt.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=y,t&&t(vt.SUBSCRIBED);return}}).receive("error",c=>{this.state=me.errored,t==null||t(vt.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(vt.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,n={}){return await this.send({type:"presence",event:"track",payload:t},n.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,n,r){return this._on(t,n,r)}async send(t,n={}){var r,i;if(!this._canPush()&&t.type==="broadcast"){const{event:s,payload:o}=t,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:s,payload:o,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(r=n.timeout)!==null&&r!==void 0?r:this.timeout);return await((i=u.body)===null||i===void 0?void 0:i.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(s=>{var o,a,l;const u=this._push(t.type,t,n.timeout||this.timeout);t.type==="broadcast"&&!(!((l=(a=(o=this.params)===null||o===void 0?void 0:o.config)===null||a===void 0?void 0:a.broadcast)===null||l===void 0)&&l.ack)&&s("ok"),u.receive("ok",()=>s("ok")),u.receive("error",()=>s("error")),u.receive("timeout",()=>s("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=me.leaving;const n=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(ut.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(i=>{r=new Xa(this,ut.leave,{},t),r.receive("ok",()=>{n(),i("ok")}).receive("timeout",()=>{n(),i("timed out")}).receive("error",()=>{i("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{r==null||r.destroy()})}teardown(){this.pushBuffer.forEach(t=>t.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(t,n,r){const i=new AbortController,s=setTimeout(()=>i.abort(),r),o=await this.socket.fetch(t,Object.assign(Object.assign({},n),{signal:i.signal}));return clearTimeout(s),o}_push(t,n,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new Xa(this,t,n,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(t,n,r){return n}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,n,r){var i,s;const o=t.toLocaleLowerCase(),{close:a,error:l,leave:u,join:c}=ut;if(r&&[a,l,u,c].indexOf(o)>=0&&r!==this._joinRef())return;let h=this._onMessage(o,n,r);if(n&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(i=this.bindings.postgres_changes)===null||i===void 0||i.filter(f=>{var y,w,x;return((y=f.filter)===null||y===void 0?void 0:y.event)==="*"||((x=(w=f.filter)===null||w===void 0?void 0:w.event)===null||x===void 0?void 0:x.toLocaleLowerCase())===o}).map(f=>f.callback(h,r)):(s=this.bindings[o])===null||s===void 0||s.filter(f=>{var y,w,x,v,p,m;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in f){const _=f.id,k=(y=f.filter)===null||y===void 0?void 0:y.event;return _&&((w=n.ids)===null||w===void 0?void 0:w.includes(_))&&(k==="*"||(k==null?void 0:k.toLocaleLowerCase())===((x=n.data)===null||x===void 0?void 0:x.type.toLocaleLowerCase()))}else{const _=(p=(v=f==null?void 0:f.filter)===null||v===void 0?void 0:v.event)===null||p===void 0?void 0:p.toLocaleLowerCase();return _==="*"||_===((m=n==null?void 0:n.event)===null||m===void 0?void 0:m.toLocaleLowerCase())}else return f.type.toLocaleLowerCase()===o}).map(f=>{if(typeof h=="object"&&"ids"in h){const y=h.data,{schema:w,table:x,commit_timestamp:v,type:p,errors:m}=y;h=Object.assign(Object.assign({},{schema:w,table:x,commit_timestamp:v,eventType:p,new:{},old:{},errors:m}),this._getPayloadRecords(y))}f.callback(h,r)})}_isClosed(){return this.state===me.closed}_isJoined(){return this.state===me.joined}_isJoining(){return this.state===me.joining}_isLeaving(){return this.state===me.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,n,r){const i=t.toLocaleLowerCase(),s={type:i,filter:n,callback:r};return this.bindings[i]?this.bindings[i].push(s):this.bindings[i]=[s],this}_off(t,n){const r=t.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(i=>{var s;return!(((s=i.type)===null||s===void 0?void 0:s.toLocaleLowerCase())===r&&ha.isEqual(i.filter,n))}),this}static isEqual(t,n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(ut.close,{},t)}_onError(t){this._on(ut.error,{},n=>t(n))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=me.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const n={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(n.new=Ah(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(n.old=Ah(t.columns,t.old_record)),n}}const Rh=()=>{},kS=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Bm{constructor(t,n){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=Dm,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Rh,this.ref=0,this.logger=Rh,this.conn=null,this.sendBuffer=[],this.serializer=new mS,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=s=>{let o;return s?o=s:typeof fetch>"u"?o=(...a)=>Ve(()=>Promise.resolve().then(()=>Yr),void 0).then(({default:l})=>l(...a)):o=fetch,(...a)=>o(...a)},this.endPoint=`${t}/${hu.websocket}`,this.httpEndpoint=zm(t),n!=null&&n.transport?this.transport=n.transport:this.transport=null,n!=null&&n.params&&(this.params=n.params),n!=null&&n.timeout&&(this.timeout=n.timeout),n!=null&&n.logger&&(this.logger=n.logger),(n!=null&&n.logLevel||n!=null&&n.log_level)&&(this.logLevel=n.logLevel||n.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),n!=null&&n.heartbeatIntervalMs&&(this.heartbeatIntervalMs=n.heartbeatIntervalMs);const i=(r=n==null?void 0:n.params)===null||r===void 0?void 0:r.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=n!=null&&n.reconnectAfterMs?n.reconnectAfterMs:s=>[1e3,2e3,5e3,1e4][s-1]||1e4,this.encode=n!=null&&n.encode?n.encode:(s,o)=>o(JSON.stringify(s)),this.decode=n!=null&&n.decode?n.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new $m(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(n==null?void 0:n.fetch),n!=null&&n.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(n==null?void 0:n.worker)||!1,this.workerUrl=n==null?void 0:n.workerUrl}this.accessToken=(n==null?void 0:n.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=cS),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:fS}))}disconnect(t,n){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,n??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(r=>r.teardown()))}getChannels(){return this.channels}async removeChannel(t){const n=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),n}async removeAllChannels(){const t=await Promise.all(this.channels.map(n=>n.unsubscribe()));return this.channels=[],this.disconnect(),t}log(t,n,r){this.logger(t,n,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Ai.connecting:return Vn.Connecting;case Ai.open:return Vn.Open;case Ai.closing:return Vn.Closing;default:return Vn.Closed}}isConnected(){return this.connectionState()===Vn.Open}channel(t,n={config:{}}){const r=`realtime:${t}`,i=this.getChannels().find(s=>s.topic===r);if(i)return i;{const s=new ha(`realtime:${t}`,n,this);return this.channels.push(s),s}}push(t){const{topic:n,event:r,payload:i,ref:s}=t,o=()=>{this.encode(t,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${n} ${r} (${s})`,i),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(t=null){let n=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=n&&(this.accessTokenValue=n,this.channels.forEach(r=>{const i={access_token:n,version:hS};n&&r.updateJoinPayload(i),r.joinedOnce&&r._isJoined()&&r._push(ut.access_token,{access_token:n})}))}async sendHeartbeat(){var t;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(t=this.conn)===null||t===void 0||t.close(pS,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(t){this.heartbeatCallback=t}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let n=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));n&&(this.log("transport",`leaving duplicate topic "${t}"`),n.unsubscribe())}_remove(t){this.channels=this.channels.filter(n=>n.topic!==t.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,n=>{let{topic:r,event:i,payload:s,ref:o}=n;r==="phoenix"&&i==="phx_reply"&&this.heartbeatCallback(n.payload.status=="ok"?"ok":"error"),o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${r} ${i} ${o&&"("+o+")"||""}`,s),Array.from(this.channels).filter(a=>a._isMember(r)).forEach(a=>a._trigger(i,s,o)),this.stateChangeCallbacks.message.forEach(a=>a(n))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(t=>t())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=n=>{this.log("worker","worker error",n.message),this.workerRef.terminate()},this.workerRef.onmessage=n=>{n.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(n=>n(t))}_onConnError(t){this.log("transport",`${t}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(n=>n(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(ut.error))}_appendParams(t,n){if(Object.keys(n).length===0)return t;const r=t.match(/\?/)?"&":"?",i=new URLSearchParams(n);return`${t}${r}${i}`}_workerObjectUrl(t){let n;if(t)n=t;else{const r=new Blob([kS],{type:"application/javascript"});n=URL.createObjectURL(r)}return n}}class Nc extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function de(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class CS extends Nc{constructor(t,n){super(t),this.name="StorageApiError",this.status=n}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class vu extends Nc{constructor(t,n){super(t),this.name="StorageUnknownError",this.originalError=n}}var ES=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const Um=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Ve(()=>Promise.resolve().then(()=>Yr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},bS=()=>ES(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield Ve(()=>Promise.resolve().then(()=>Yr),void 0)).Response:Response}),yu=e=>{if(Array.isArray(e))return e.map(n=>yu(n));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([n,r])=>{const i=n.replace(/([-_][a-z])/gi,s=>s.toUpperCase().replace(/[-_]/g,""));t[i]=yu(r)}),t};var er=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const Za=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),TS=(e,t,n)=>er(void 0,void 0,void 0,function*(){const r=yield bS();e instanceof r&&!(n!=null&&n.noResolveJson)?e.json().then(i=>{t(new CS(Za(i),e.status||500))}).catch(i=>{t(new vu(Za(i),i))}):t(new vu(Za(e),e))}),PS=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),n))};function cs(e,t,n,r,i,s){return er(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(n,PS(t,r,i,s)).then(l=>{if(!l.ok)throw l;return r!=null&&r.noResolveJson?l:l.json()}).then(l=>o(l)).catch(l=>TS(l,a,r))})})}function Ao(e,t,n,r){return er(this,void 0,void 0,function*(){return cs(e,"GET",t,n,r)})}function Jt(e,t,n,r,i){return er(this,void 0,void 0,function*(){return cs(e,"POST",t,r,i,n)})}function jS(e,t,n,r,i){return er(this,void 0,void 0,function*(){return cs(e,"PUT",t,r,i,n)})}function AS(e,t,n,r){return er(this,void 0,void 0,function*(){return cs(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}function Fm(e,t,n,r,i){return er(this,void 0,void 0,function*(){return cs(e,"DELETE",t,r,i,n)})}var Ae=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const RS={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Oh={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class OS{constructor(t,n={},r,i){this.url=t,this.headers=n,this.bucketId=r,this.fetch=Um(i)}uploadOrUpdate(t,n,r,i){return Ae(this,void 0,void 0,function*(){try{let s;const o=Object.assign(Object.assign({},Oh),i);let a=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(o.upsert)});const l=o.metadata;typeof Blob<"u"&&r instanceof Blob?(s=new FormData,s.append("cacheControl",o.cacheControl),l&&s.append("metadata",this.encodeMetadata(l)),s.append("",r)):typeof FormData<"u"&&r instanceof FormData?(s=r,s.append("cacheControl",o.cacheControl),l&&s.append("metadata",this.encodeMetadata(l))):(s=r,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),i!=null&&i.headers&&(a=Object.assign(Object.assign({},a),i.headers));const u=this._removeEmptyFolders(n),c=this._getFinalPath(u),d=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:t,body:s,headers:a},o!=null&&o.duplex?{duplex:o.duplex}:{})),h=yield d.json();return d.ok?{data:{path:u,id:h.Id,fullPath:h.Key},error:null}:{data:null,error:h}}catch(s){if(de(s))return{data:null,error:s};throw s}})}upload(t,n,r){return Ae(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,n,r)})}uploadToSignedUrl(t,n,r,i){return Ae(this,void 0,void 0,function*(){const s=this._removeEmptyFolders(t),o=this._getFinalPath(s),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",n);try{let l;const u=Object.assign({upsert:Oh.upsert},i),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&r instanceof Blob?(l=new FormData,l.append("cacheControl",u.cacheControl),l.append("",r)):typeof FormData<"u"&&r instanceof FormData?(l=r,l.append("cacheControl",u.cacheControl)):(l=r,c["cache-control"]=`max-age=${u.cacheControl}`,c["content-type"]=u.contentType);const d=yield this.fetch(a.toString(),{method:"PUT",body:l,headers:c}),h=yield d.json();return d.ok?{data:{path:s,fullPath:h.Key},error:null}:{data:null,error:h}}catch(l){if(de(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(t,n){return Ae(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const i=Object.assign({},this.headers);n!=null&&n.upsert&&(i["x-upsert"]="true");const s=yield Jt(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),o=new URL(this.url+s.url),a=o.searchParams.get("token");if(!a)throw new Nc("No token returned by API");return{data:{signedUrl:o.toString(),path:t,token:a},error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}update(t,n,r){return Ae(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,n,r)})}move(t,n,r){return Ae(this,void 0,void 0,function*(){try{return{data:yield Jt(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}copy(t,n,r){return Ae(this,void 0,void 0,function*(){try{return{data:{path:(yield Jt(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}createSignedUrl(t,n,r){return Ae(this,void 0,void 0,function*(){try{let i=this._getFinalPath(t),s=yield Jt(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:n},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return s={signedUrl:encodeURI(`${this.url}${s.signedURL}${o}`)},{data:s,error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}createSignedUrls(t,n,r){return Ae(this,void 0,void 0,function*(){try{const i=yield Jt(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:n,paths:t},{headers:this.headers}),s=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:i.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${s}`):null})),error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}download(t,n){return Ae(this,void 0,void 0,function*(){const i=typeof(n==null?void 0:n.transform)<"u"?"render/image/authenticated":"object",s=this.transformOptsToQueryString((n==null?void 0:n.transform)||{}),o=s?`?${s}`:"";try{const a=this._getFinalPath(t);return{data:yield(yield Ao(this.fetch,`${this.url}/${i}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(de(a))return{data:null,error:a};throw a}})}info(t){return Ae(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{const r=yield Ao(this.fetch,`${this.url}/object/info/${n}`,{headers:this.headers});return{data:yu(r),error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}exists(t){return Ae(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{return yield AS(this.fetch,`${this.url}/object/${n}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(de(r)&&r instanceof vu){const i=r.originalError;if([400,404].includes(i==null?void 0:i.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,n){const r=this._getFinalPath(t),i=[],s=n!=null&&n.download?`download=${n.download===!0?"":n.download}`:"";s!==""&&i.push(s);const a=typeof(n==null?void 0:n.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((n==null?void 0:n.transform)||{});l!==""&&i.push(l);let u=i.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${r}${u}`)}}}remove(t){return Ae(this,void 0,void 0,function*(){try{return{data:yield Fm(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}list(t,n,r){return Ae(this,void 0,void 0,function*(){try{const i=Object.assign(Object.assign(Object.assign({},RS),n),{prefix:t||""});return{data:yield Jt(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const n=[];return t.width&&n.push(`width=${t.width}`),t.height&&n.push(`height=${t.height}`),t.resize&&n.push(`resize=${t.resize}`),t.format&&n.push(`format=${t.format}`),t.quality&&n.push(`quality=${t.quality}`),n.join("&")}}const LS="2.7.1",IS={"X-Client-Info":`storage-js/${LS}`};var sr=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class NS{constructor(t,n={},r){this.url=t,this.headers=Object.assign(Object.assign({},IS),n),this.fetch=Um(r)}listBuckets(){return sr(this,void 0,void 0,function*(){try{return{data:yield Ao(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(de(t))return{data:null,error:t};throw t}})}getBucket(t){return sr(this,void 0,void 0,function*(){try{return{data:yield Ao(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}createBucket(t,n={public:!1}){return sr(this,void 0,void 0,function*(){try{return{data:yield Jt(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}updateBucket(t,n){return sr(this,void 0,void 0,function*(){try{return{data:yield jS(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}emptyBucket(t){return sr(this,void 0,void 0,function*(){try{return{data:yield Jt(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}deleteBucket(t){return sr(this,void 0,void 0,function*(){try{return{data:yield Fm(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}}class MS extends NS{constructor(t,n={},r){super(t,n,r)}from(t){return new OS(this.url,this.headers,t,this.fetch)}}const DS="2.50.2";let mi="";typeof Deno<"u"?mi="deno":typeof document<"u"?mi="web":typeof navigator<"u"&&navigator.product==="ReactNative"?mi="react-native":mi="node";const $S={"X-Client-Info":`supabase-js-${mi}/${DS}`},VS={headers:$S},zS={schema:"public"},BS={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},US={};var FS=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const HS=e=>{let t;return e?t=e:typeof fetch>"u"?t=bm:t=fetch,(...n)=>t(...n)},WS=()=>typeof Headers>"u"?Tm:Headers,KS=(e,t,n)=>{const r=HS(n),i=WS();return(s,o)=>FS(void 0,void 0,void 0,function*(){var a;const l=(a=yield t())!==null&&a!==void 0?a:e;let u=new i(o==null?void 0:o.headers);return u.has("apikey")||u.set("apikey",e),u.has("Authorization")||u.set("Authorization",`Bearer ${l}`),r(s,Object.assign(Object.assign({},o),{headers:u}))})};var GS=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};function qS(e){return e.endsWith("/")?e:e+"/"}function JS(e,t){var n,r;const{db:i,auth:s,realtime:o,global:a}=e,{db:l,auth:u,realtime:c,global:d}=t,h={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},u),s),realtime:Object.assign(Object.assign({},c),o),global:Object.assign(Object.assign(Object.assign({},d),a),{headers:Object.assign(Object.assign({},(n=d==null?void 0:d.headers)!==null&&n!==void 0?n:{}),(r=a==null?void 0:a.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>GS(this,void 0,void 0,function*(){return""})};return e.accessToken?h.accessToken=e.accessToken:delete h.accessToken,h}const Hm="2.70.0",lr=30*1e3,wu=3,el=wu*lr,QS="http://localhost:9999",YS="supabase.auth.token",XS={"X-Client-Info":`gotrue-js/${Hm}`},xu="X-Supabase-Api-Version",Wm={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},ZS=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,e_=6e5;class fa extends Error{constructor(t,n,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=n,this.code=r}}function M(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class Km extends fa{constructor(t,n,r){super(t,n,r),this.name="AuthApiError",this.status=n,this.code=r}}function Gm(e){return M(e)&&e.name==="AuthApiError"}class Mc extends fa{constructor(t,n){super(t),this.name="AuthUnknownError",this.originalError=n}}class Ft extends fa{constructor(t,n,r,i){super(t,r,i),this.name=n,this.status=r}}class Tt extends Ft{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function qm(e){return M(e)&&e.name==="AuthSessionMissingError"}class gi extends Ft{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class vi extends Ft{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class yi extends Ft{constructor(t,n=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function Jm(e){return M(e)&&e.name==="AuthImplicitGrantRedirectError"}class Su extends Ft{constructor(t,n=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Ro extends Ft{constructor(t,n){super(t,"AuthRetryableFetchError",n,void 0)}}function Xs(e){return M(e)&&e.name==="AuthRetryableFetchError"}class _u extends Ft{constructor(t,n,r){super(t,"AuthWeakPasswordError",n,"weak_password"),this.reasons=r}}function t_(e){return M(e)&&e.name==="AuthWeakPasswordError"}class Lr extends Ft{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const Oo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Lh=` 	
\r=`.split(""),n_=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Lh.length;t+=1)e[Lh[t].charCodeAt(0)]=-2;for(let t=0;t<Oo.length;t+=1)e[Oo[t].charCodeAt(0)]=t;return e})();function Ih(e,t,n){if(e!==null)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(Oo[r]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(Oo[r]),t.queuedBits-=6}}function Qm(e,t,n){const r=n_[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function Nh(e){const t=[],n=o=>{t.push(String.fromCodePoint(o))},r={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},s=o=>{s_(o,r,n)};for(let o=0;o<e.length;o+=1)Qm(e.charCodeAt(o),i,s);return t.join("")}function r_(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function i_(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const i=(r-55296)*1024&65535;r=(e.charCodeAt(n+1)-56320&65535|i)+65536,n+=1}r_(r,t)}}function s_(e,t,n){if(t.utf8seq===0){if(e<=127){n(e);return}for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&n(t.codepoint)}}function o_(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};for(let i=0;i<e.length;i+=1)Qm(e.charCodeAt(i),n,r);return new Uint8Array(t)}function a_(e){const t=[];return i_(e,n=>t.push(n)),new Uint8Array(t)}function l_(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};return e.forEach(i=>Ih(i,n,r)),Ih(null,n,r),t.join("")}function u_(e){return Math.round(Date.now()/1e3)+e}function c_(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const ot=()=>typeof window<"u"&&typeof document<"u",An={tested:!1,writable:!1},Ri=()=>{if(!ot())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(An.tested)return An.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),An.tested=!0,An.writable=!0}catch{An.tested=!0,An.writable=!1}return An.writable};function d_(e){const t={},n=new URL(e);if(n.hash&&n.hash[0]==="#")try{new URLSearchParams(n.hash.substring(1)).forEach((i,s)=>{t[s]=i})}catch{}return n.searchParams.forEach((r,i)=>{t[i]=r}),t}const Ym=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Ve(()=>Promise.resolve().then(()=>Yr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},h_=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",Xm=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},Ls=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch{return n}},Is=async(e,t)=>{await e.removeItem(t)};class pa{constructor(){this.promise=new pa.promiseConstructor((t,n)=>{this.resolve=t,this.reject=n})}}pa.promiseConstructor=Promise;function tl(e){const t=e.split(".");if(t.length!==3)throw new Lr("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!ZS.test(t[r]))throw new Lr("JWT not in base64url format");return{header:JSON.parse(Nh(t[0])),payload:JSON.parse(Nh(t[1])),signature:o_(t[2]),raw:{header:t[0],payload:t[1]}}}async function f_(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function p_(e,t){return new Promise((r,i)=>{(async()=>{for(let s=0;s<1/0;s++)try{const o=await e(s);if(!t(s,null,o)){r(o);return}}catch(o){if(!t(s,o)){i(o);return}}})()})}function m_(e){return("0"+e.toString(16)).substr(-2)}function g_(){const t=new Uint32Array(56);if(typeof crypto>"u"){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=n.length;let i="";for(let s=0;s<56;s++)i+=n.charAt(Math.floor(Math.random()*r));return i}return crypto.getRandomValues(t),Array.from(t,m_).join("")}async function v_(e){const n=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",n),i=new Uint8Array(r);return Array.from(i).map(s=>String.fromCharCode(s)).join("")}async function y_(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const n=await v_(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function or(e,t,n=!1){const r=g_();let i=r;n&&(i+="/PASSWORD_RECOVERY"),await Xm(e,`${t}-code-verifier`,i);const s=await y_(r);return[s,r===s?"plain":"s256"]}const w_=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function x_(e){const t=e.headers.get(xu);if(!t||!t.match(w_))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function S_(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function __(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const k_=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function ar(e){if(!k_.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var C_=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};const In=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),E_=[502,503,504];async function Mh(e){var t;if(!h_(e))throw new Ro(In(e),0);if(E_.includes(e.status))throw new Ro(In(e),e.status);let n;try{n=await e.json()}catch(s){throw new Mc(In(s),s)}let r;const i=x_(e);if(i&&i.getTime()>=Wm["2024-01-01"].timestamp&&typeof n=="object"&&n&&typeof n.code=="string"?r=n.code:typeof n=="object"&&n&&typeof n.error_code=="string"&&(r=n.error_code),r){if(r==="weak_password")throw new _u(In(n),e.status,((t=n.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new Tt}else if(typeof n=="object"&&n&&typeof n.weak_password=="object"&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((s,o)=>s&&typeof o=="string",!0))throw new _u(In(n),e.status,n.weak_password.reasons);throw new Km(In(n),e.status||500,r)}const b_=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),n))};async function $(e,t,n,r){var i;const s=Object.assign({},r==null?void 0:r.headers);s[xu]||(s[xu]=Wm["2024-01-01"].name),r!=null&&r.jwt&&(s.Authorization=`Bearer ${r.jwt}`);const o=(i=r==null?void 0:r.query)!==null&&i!==void 0?i:{};r!=null&&r.redirectTo&&(o.redirect_to=r.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await T_(e,t,n+a,{headers:s,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(l):{data:Object.assign({},l),error:null}}async function T_(e,t,n,r,i,s){const o=b_(t,r,i,s);let a;try{a=await e(n,Object.assign({},o))}catch(l){throw console.error(l),new Ro(In(l),0)}if(a.ok||await Mh(a),r!=null&&r.noResolveJson)return a;try{return await a.json()}catch(l){await Mh(l)}}function Et(e){var t;let n=null;R_(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=u_(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:n,user:r},error:null}}function Dh(e){const t=Et(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((n,r)=>n&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function Zt(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function P_(e){return{data:e,error:null}}function j_(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:s}=e,o=C_(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:s},l=Object.assign({},o);return{data:{properties:a,user:l},error:null}}function A_(e){return e}function R_(e){return e.access_token&&e.refresh_token&&e.expires_in}const Zs=["global","local","others"];var O_=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};class Dc{constructor({url:t="",headers:n={},fetch:r}){this.url=t,this.headers=n,this.fetch=Ym(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,n=Zs[0]){if(Zs.indexOf(n)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Zs.join(", ")}`);try{return await $(this.fetch,"POST",`${this.url}/logout?scope=${n}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(M(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,n={}){try{return await $(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:n.data},headers:this.headers,redirectTo:n.redirectTo,xform:Zt})}catch(r){if(M(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:n}=t,r=O_(t,["options"]),i=Object.assign(Object.assign({},r),n);return"newEmail"in r&&(i.new_email=r==null?void 0:r.newEmail,delete i.newEmail),await $(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:j_,redirectTo:n==null?void 0:n.redirectTo})}catch(n){if(M(n))return{data:{properties:null,user:null},error:n};throw n}}async createUser(t){try{return await $(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:Zt})}catch(n){if(M(n))return{data:{user:null},error:n};throw n}}async listUsers(t){var n,r,i,s,o,a,l;try{const u={nextPage:null,lastPage:0,total:0},c=await $(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(n=t==null?void 0:t.page)===null||n===void 0?void 0:n.toString())!==null&&r!==void 0?r:"",per_page:(s=(i=t==null?void 0:t.perPage)===null||i===void 0?void 0:i.toString())!==null&&s!==void 0?s:""},xform:A_});if(c.error)throw c.error;const d=await c.json(),h=(o=c.headers.get("x-total-count"))!==null&&o!==void 0?o:0,f=(l=(a=c.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return f.length>0&&(f.forEach(y=>{const w=parseInt(y.split(";")[0].split("=")[1].substring(0,1)),x=JSON.parse(y.split(";")[1].split("=")[1]);u[`${x}Page`]=w}),u.total=parseInt(h)),{data:Object.assign(Object.assign({},d),u),error:null}}catch(u){if(M(u))return{data:{users:[]},error:u};throw u}}async getUserById(t){ar(t);try{return await $(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:Zt})}catch(n){if(M(n))return{data:{user:null},error:n};throw n}}async updateUserById(t,n){ar(t);try{return await $(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:n,headers:this.headers,xform:Zt})}catch(r){if(M(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,n=!1){ar(t);try{return await $(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:n},xform:Zt})}catch(r){if(M(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){ar(t.userId);try{const{data:n,error:r}=await $(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:i=>({data:{factors:i},error:null})});return{data:n,error:r}}catch(n){if(M(n))return{data:null,error:n};throw n}}async _deleteFactor(t){ar(t.userId),ar(t.id);try{return{data:await $(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(n){if(M(n))return{data:null,error:n};throw n}}}const L_={getItem:e=>Ri()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Ri()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Ri()&&globalThis.localStorage.removeItem(e)}};function $h(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}function I_(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const Nn={debug:!!(globalThis&&Ri()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class $c extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class Zm extends $c{}class N_ extends $c{}async function eg(e,t,n){Nn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),Nn.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async i=>{if(i){Nn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await n()}finally{Nn.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(t===0)throw Nn.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Zm(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(Nn.debug)try{const s=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(s,null,"  "))}catch(s){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",s)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}}))}const Vh={};async function M_(e,t,n){var r;const i=(r=Vh[e])!==null&&r!==void 0?r:Promise.resolve(),s=Promise.race([i.catch(()=>null),t>=0?new Promise((o,a)=>{setTimeout(()=>{a(new N_(`Acquring process lock with name "${e}" timed out`))},t)}):null].filter(o=>o)).catch(o=>{if(o&&o.isAcquireTimeout)throw o;return null}).then(async()=>await n());return Vh[e]=s.catch(async o=>{if(o&&o.isAcquireTimeout)return await i,null;throw o}),await s}I_();const D_={url:QS,storageKey:YS,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:XS,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function zh(e,t,n){return await n()}class Fr{constructor(t){var n,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Fr.nextInstanceID,Fr.nextInstanceID+=1,this.instanceID>0&&ot()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const i=Object.assign(Object.assign({},D_),t);if(this.logDebugMessages=!!i.debug,typeof i.debug=="function"&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new Dc({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=Ym(i.fetch),this.lock=i.lock||zh,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:ot()&&(!((n=globalThis==null?void 0:globalThis.navigator)===null||n===void 0)&&n.locks)?this.lock=eg:this.lock=zh,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:Ri()?this.storage=L_:(this.memoryStorage={},this.storage=$h(this.memoryStorage)):(this.memoryStorage={},this.storage=$h(this.memoryStorage)),ot()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(s){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",s)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async s=>{this._debug("received broadcast notification from other tab or client",s),await this._notifyAllSubscribers(s.data.event,s.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Hm}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const n=d_(window.location.href);let r="none";if(this._isImplicitGrantCallback(n)?r="implicit":await this._isPKCECallback(n)&&(r="pkce"),ot()&&this.detectSessionInUrl&&r!=="none"){const{data:i,error:s}=await this._getSessionFromURL(n,r);if(s){if(this._debug("#_initialize()","error detecting session from URL",s),Jm(s)){const l=(t=s.details)===null||t===void 0?void 0:t.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:s}}return await this._removeSession(),{error:s}}const{session:o,redirectType:a}=i;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),await this._saveSession(o),setTimeout(async()=>{a==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(n){return M(n)?{error:n}:{error:new Mc("Unexpected error during initialization",n)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var n,r,i;try{const s=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(i=t==null?void 0:t.options)===null||i===void 0?void 0:i.captchaToken}},xform:Et}),{data:o,error:a}=s;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(s){if(M(s))return{data:{user:null,session:null},error:s};throw s}}async signUp(t){var n,r,i;try{let s;if("email"in t){const{email:c,password:d,options:h}=t;let f=null,y=null;this.flowType==="pkce"&&([f,y]=await or(this.storage,this.storageKey)),s=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:h==null?void 0:h.emailRedirectTo,body:{email:c,password:d,data:(n=h==null?void 0:h.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:h==null?void 0:h.captchaToken},code_challenge:f,code_challenge_method:y},xform:Et})}else if("phone"in t){const{phone:c,password:d,options:h}=t;s=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:d,data:(r=h==null?void 0:h.data)!==null&&r!==void 0?r:{},channel:(i=h==null?void 0:h.channel)!==null&&i!==void 0?i:"sms",gotrue_meta_security:{captcha_token:h==null?void 0:h.captchaToken}},xform:Et})}else throw new vi("You must provide either an email or phone number and a password");const{data:o,error:a}=s;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(s){if(M(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithPassword(t){try{let n;if("email"in t){const{email:s,password:o,options:a}=t;n=await $(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:Dh})}else if("phone"in t){const{phone:s,password:o,options:a}=t;n=await $(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:Dh})}else throw new vi("You must provide either an email or phone number and a password");const{data:r,error:i}=n;return i?{data:{user:null,session:null},error:i}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new gi}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i})}catch(n){if(M(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOAuth(t){var n,r,i,s;return await this._handleProviderSignIn(t.provider,{redirectTo:(n=t.options)===null||n===void 0?void 0:n.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(i=t.options)===null||i===void 0?void 0:i.queryParams,skipBrowserRedirect:(s=t.options)===null||s===void 0?void 0:s.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async signInWithWeb3(t){const{chain:n}=t;if(n==="solana")return await this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${n}"`)}async signInWithSolana(t){var n,r,i,s,o,a,l,u,c,d,h,f;let y,w;if("message"in t)y=t.message,w=t.signature;else{const{chain:x,wallet:v,statement:p,options:m}=t;let _;if(ot())if(typeof v=="object")_=v;else{const C=window;if("solana"in C&&typeof C.solana=="object"&&("signIn"in C.solana&&typeof C.solana.signIn=="function"||"signMessage"in C.solana&&typeof C.solana.signMessage=="function"))_=C.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof v!="object"||!(m!=null&&m.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");_=v}const k=new URL((n=m==null?void 0:m.url)!==null&&n!==void 0?n:window.location.href);if("signIn"in _&&_.signIn){const C=await _.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},m==null?void 0:m.signInWithSolana),{version:"1",domain:k.host,uri:k.href}),p?{statement:p}:null));let E;if(Array.isArray(C)&&C[0]&&typeof C[0]=="object")E=C[0];else if(C&&typeof C=="object"&&"signedMessage"in C&&"signature"in C)E=C;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in E&&"signature"in E&&(typeof E.signedMessage=="string"||E.signedMessage instanceof Uint8Array)&&E.signature instanceof Uint8Array)y=typeof E.signedMessage=="string"?E.signedMessage:new TextDecoder().decode(E.signedMessage),w=E.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in _)||typeof _.signMessage!="function"||!("publicKey"in _)||typeof _!="object"||!_.publicKey||!("toBase58"in _.publicKey)||typeof _.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");y=[`${k.host} wants you to sign in with your Solana account:`,_.publicKey.toBase58(),...p?["",p,""]:[""],"Version: 1",`URI: ${k.href}`,`Issued At: ${(i=(r=m==null?void 0:m.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&i!==void 0?i:new Date().toISOString()}`,...!((s=m==null?void 0:m.signInWithSolana)===null||s===void 0)&&s.notBefore?[`Not Before: ${m.signInWithSolana.notBefore}`]:[],...!((o=m==null?void 0:m.signInWithSolana)===null||o===void 0)&&o.expirationTime?[`Expiration Time: ${m.signInWithSolana.expirationTime}`]:[],...!((a=m==null?void 0:m.signInWithSolana)===null||a===void 0)&&a.chainId?[`Chain ID: ${m.signInWithSolana.chainId}`]:[],...!((l=m==null?void 0:m.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${m.signInWithSolana.nonce}`]:[],...!((u=m==null?void 0:m.signInWithSolana)===null||u===void 0)&&u.requestId?[`Request ID: ${m.signInWithSolana.requestId}`]:[],...!((d=(c=m==null?void 0:m.signInWithSolana)===null||c===void 0?void 0:c.resources)===null||d===void 0)&&d.length?["Resources",...m.signInWithSolana.resources.map(E=>`- ${E}`)]:[]].join(`
`);const C=await _.signMessage(new TextEncoder().encode(y),"utf8");if(!C||!(C instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");w=C}}try{const{data:x,error:v}=await $(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:y,signature:l_(w)},!((h=t.options)===null||h===void 0)&&h.captchaToken?{gotrue_meta_security:{captcha_token:(f=t.options)===null||f===void 0?void 0:f.captchaToken}}:null),xform:Et});if(v)throw v;return!x||!x.session||!x.user?{data:{user:null,session:null},error:new gi}:(x.session&&(await this._saveSession(x.session),await this._notifyAllSubscribers("SIGNED_IN",x.session)),{data:Object.assign({},x),error:v})}catch(x){if(M(x))return{data:{user:null,session:null},error:x};throw x}}async _exchangeCodeForSession(t){const n=await Ls(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(n??"").split("/");try{const{data:s,error:o}=await $(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:Et});if(await Is(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!s||!s.session||!s.user?{data:{user:null,session:null,redirectType:null},error:new gi}:(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign(Object.assign({},s),{redirectType:i??null}),error:o})}catch(s){if(M(s))return{data:{user:null,session:null,redirectType:null},error:s};throw s}}async signInWithIdToken(t){try{const{options:n,provider:r,token:i,access_token:s,nonce:o}=t,a=await $(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:s,nonce:o,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},xform:Et}),{data:l,error:u}=a;return u?{data:{user:null,session:null},error:u}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new gi}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:u})}catch(n){if(M(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOtp(t){var n,r,i,s,o;try{if("email"in t){const{email:a,options:l}=t;let u=null,c=null;this.flowType==="pkce"&&([u,c]=await or(this.storage,this.storageKey));const{error:d}=await $(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(n=l==null?void 0:l.data)!==null&&n!==void 0?n:{},create_user:(r=l==null?void 0:l.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:u,code_challenge_method:c},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:d}}if("phone"in t){const{phone:a,options:l}=t,{data:u,error:c}=await $(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(i=l==null?void 0:l.data)!==null&&i!==void 0?i:{},create_user:(s=l==null?void 0:l.shouldCreateUser)!==null&&s!==void 0?s:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(o=l==null?void 0:l.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:u==null?void 0:u.message_id},error:c}}throw new vi("You must provide either an email or phone number.")}catch(a){if(M(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(t){var n,r;try{let i,s;"options"in t&&(i=(n=t.options)===null||n===void 0?void 0:n.redirectTo,s=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:o,error:a}=await $(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:s}}),redirectTo:i,xform:Et});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const l=o.session,u=o.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(M(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithSSO(t){var n,r,i;try{let s=null,o=null;return this.flowType==="pkce"&&([s,o]=await or(this.storage,this.storageKey)),await $(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(n=t.options)===null||n===void 0?void 0:n.redirectTo)!==null&&r!==void 0?r:void 0}),!((i=t==null?void 0:t.options)===null||i===void 0)&&i.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:s,code_challenge_method:o}),headers:this.headers,xform:P_})}catch(s){if(M(s))return{data:null,error:s};throw s}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:n},error:r}=t;if(r)throw r;if(!n)throw new Tt;const{error:i}=await $(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:n.access_token});return{data:{user:null,session:null},error:i}})}catch(t){if(M(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const n=`${this.url}/resend`;if("email"in t){const{email:r,type:i,options:s}=t,{error:o}=await $(this.fetch,"POST",n,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}},redirectTo:s==null?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in t){const{phone:r,type:i,options:s}=t,{data:o,error:a}=await $(this.fetch,"POST",n,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:a}}throw new vi("You must provide either an email or phone number and a type")}catch(n){if(M(n))return{data:{user:null,session:null},error:n};throw n}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async n=>n))}async _acquireLock(t,n){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),i=(async()=>(await r,await n()))();return this.pendingInLock.push((async()=>{try{await i}catch{}})()),i}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=n();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const i=[...this.pendingInLock];await Promise.all(i),this.pendingInLock.splice(0,i.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const n=await this.__loadSession();return await t(n)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const n=await Ls(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",n),n!==null&&(this._isValidSession(n)?t=n:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at*1e3-Date.now()<el:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.storage.isServer){let o=this.suppressGetSessionWarning;t=new Proxy(t,{get:(l,u,c)=>(!o&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,u,c))})}return{data:{session:t},error:null}}const{session:i,error:s}=await this._callRefreshToken(t.refresh_token);return s?{data:{session:null},error:s}:{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await $(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:Zt}):await this._useSession(async n=>{var r,i,s;const{data:o,error:a}=n;if(a)throw a;return!(!((r=o.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new Tt}:await $(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(s=(i=o.session)===null||i===void 0?void 0:i.access_token)!==null&&s!==void 0?s:void 0,xform:Zt})})}catch(n){if(M(n))return qm(n)&&(await this._removeSession(),await Is(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:n};throw n}}async updateUser(t,n={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,n))}async _updateUser(t,n={}){try{return await this._useSession(async r=>{const{data:i,error:s}=r;if(s)throw s;if(!i.session)throw new Tt;const o=i.session;let a=null,l=null;this.flowType==="pkce"&&t.email!=null&&([a,l]=await or(this.storage,this.storageKey));const{data:u,error:c}=await $(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:n==null?void 0:n.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:a,code_challenge_method:l}),jwt:o.access_token,xform:Zt});if(c)throw c;return o.user=u.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(r){if(M(r))return{data:{user:null},error:r};throw r}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new Tt;const n=Date.now()/1e3;let r=n,i=!0,s=null;const{payload:o}=tl(t.access_token);if(o.exp&&(r=o.exp,i=r<=n),i){const{session:a,error:l}=await this._callRefreshToken(t.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!a)return{data:{user:null,session:null},error:null};s=a}else{const{data:a,error:l}=await this._getUser(t.access_token);if(l)throw l;s={access_token:t.access_token,refresh_token:t.refresh_token,user:a.user,token_type:"bearer",expires_in:r-n,expires_at:r},await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)}return{data:{user:s.user,session:s},error:null}}catch(n){if(M(n))return{data:{session:null,user:null},error:n};throw n}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async n=>{var r;if(!t){const{data:o,error:a}=n;if(a)throw a;t=(r=o.session)!==null&&r!==void 0?r:void 0}if(!(t!=null&&t.refresh_token))throw new Tt;const{session:i,error:s}=await this._callRefreshToken(t.refresh_token);return s?{data:{user:null,session:null},error:s}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(n){if(M(n))return{data:{user:null,session:null},error:n};throw n}}async _getSessionFromURL(t,n){try{if(!ot())throw new yi("No browser detected.");if(t.error||t.error_description||t.error_code)throw new yi(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(n){case"implicit":if(this.flowType==="pkce")throw new Su("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new yi("Not a valid implicit grant flow url.");break;default:}if(n==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new Su("No code detected.");const{data:p,error:m}=await this._exchangeCodeForSession(t.code);if(m)throw m;const _=new URL(window.location.href);return _.searchParams.delete("code"),window.history.replaceState(window.history.state,"",_.toString()),{data:{session:p.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:i,access_token:s,refresh_token:o,expires_in:a,expires_at:l,token_type:u}=t;if(!s||!a||!o||!u)throw new yi("No session defined in URL");const c=Math.round(Date.now()/1e3),d=parseInt(a);let h=c+d;l&&(h=parseInt(l));const f=h-c;f*1e3<=lr&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${f}s, should have been closer to ${d}s`);const y=h-d;c-y>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",y,h,c):c-y<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",y,h,c);const{data:w,error:x}=await this._getUser(s);if(x)throw x;const v={provider_token:r,provider_refresh_token:i,access_token:s,expires_in:d,expires_at:h,refresh_token:o,token_type:u,user:w.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:t.type},error:null}}catch(r){if(M(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const n=await Ls(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&n)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async n=>{var r;const{data:i,error:s}=n;if(s)return{error:s};const o=(r=i.session)===null||r===void 0?void 0:r.access_token;if(o){const{error:a}=await this.admin.signOut(o,t);if(a&&!(Gm(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return t!=="others"&&(await this._removeSession(),await Is(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const n=c_(),r={id:n,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",n),this.stateChangeEmitters.delete(n)}};return this._debug("#onAuthStateChange()","registered callback with id",n),this.stateChangeEmitters.set(n,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(n)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async n=>{var r,i;try{const{data:{session:s},error:o}=n;if(o)throw o;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",s)),this._debug("INITIAL_SESSION","callback id",t,"session",s)}catch(s){await((i=this.stateChangeEmitters.get(t))===null||i===void 0?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",s),console.error(s)}})}async resetPasswordForEmail(t,n={}){let r=null,i=null;this.flowType==="pkce"&&([r,i]=await or(this.storage,this.storageKey,!0));try{return await $(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:n.captchaToken}},headers:this.headers,redirectTo:n.redirectTo})}catch(s){if(M(s))return{data:null,error:s};throw s}}async getUserIdentities(){var t;try{const{data:n,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=n.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(n){if(M(n))return{data:null,error:n};throw n}}async linkIdentity(t){var n;try{const{data:r,error:i}=await this._useSession(async s=>{var o,a,l,u,c;const{data:d,error:h}=s;if(h)throw h;const f=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(o=t.options)===null||o===void 0?void 0:o.redirectTo,scopes:(a=t.options)===null||a===void 0?void 0:a.scopes,queryParams:(l=t.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await $(this.fetch,"GET",f,{headers:this.headers,jwt:(c=(u=d.session)===null||u===void 0?void 0:u.access_token)!==null&&c!==void 0?c:void 0})});if(i)throw i;return ot()&&!(!((n=t.options)===null||n===void 0)&&n.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:t.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(M(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async n=>{var r,i;const{data:s,error:o}=n;if(o)throw o;return await $(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(i=(r=s.session)===null||r===void 0?void 0:r.access_token)!==null&&i!==void 0?i:void 0})})}catch(n){if(M(n))return{data:null,error:n};throw n}}async _refreshAccessToken(t){const n=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{const r=Date.now();return await p_(async i=>(i>0&&await f_(200*Math.pow(2,i-1)),this._debug(n,"refreshing attempt",i),await $(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:Et})),(i,s)=>{const o=200*Math.pow(2,i);return s&&Xs(s)&&Date.now()+o-r<lr})}catch(r){if(this._debug(n,"error",r),M(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(n,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,n){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:n.redirectTo,scopes:n.scopes,queryParams:n.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",n,"url",r),ot()&&!n.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t;const n="#_recoverAndRefresh()";this._debug(n,"begin");try{const r=await Ls(this.storage,this.storageKey);if(this._debug(n,"session from storage",r),!this._isValidSession(r)){this._debug(n,"session is not valid"),r!==null&&await this._removeSession();return}const i=((t=r.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<el;if(this._debug(n,`session has${i?"":" not"} expired with margin of ${el}s`),i){if(this.autoRefreshToken&&r.refresh_token){const{error:s}=await this._callRefreshToken(r.refresh_token);s&&(console.error(s),Xs(s)||(this._debug(n,"refresh failed with a non-retryable error, removing the session",s),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(n,"error",r),console.error(r);return}finally{this._debug(n,"end")}}async _callRefreshToken(t){var n,r;if(!t)throw new Tt;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const i=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new pa;const{data:s,error:o}=await this._refreshAccessToken(t);if(o)throw o;if(!s.session)throw new Tt;await this._saveSession(s.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",s.session);const a={session:s.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(s){if(this._debug(i,"error",s),M(s)){const o={session:null,error:s};return Xs(s)||await this._removeSession(),(n=this.refreshingDeferred)===null||n===void 0||n.resolve(o),o}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(s),s}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(t,n,r=!0){const i=`#_notifyAllSubscribers(${t})`;this._debug(i,"begin",n,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:n});const s=[],o=Array.from(this.stateChangeEmitters.values()).map(async a=>{try{await a.callback(t,n)}catch(l){s.push(l)}});if(await Promise.all(o),s.length>0){for(let a=0;a<s.length;a+=1)console.error(s[a]);throw s[0]}}finally{this._debug(i,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await Xm(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await Is(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&ot()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(n){console.error("removing visibilitychange callback failed",n)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),lr);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async n=>{const{data:{session:r}}=n;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const i=Math.floor((r.expires_at*1e3-t)/lr);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts ${lr}ms, refresh threshold is ${wu} ticks`),i<=wu&&await this._callRefreshToken(r.refresh_token)})}catch(n){console.error("Auto refresh tick failed with error. This is likely a transient error.",n)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof $c)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!ot()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const n=`#_onVisibilityChanged(${t})`;this._debug(n,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(n,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,n,r){const i=[`provider=${encodeURIComponent(n)}`];if(r!=null&&r.redirectTo&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[s,o]=await or(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(s)}`,code_challenge_method:`${encodeURIComponent(o)}`});i.push(a.toString())}if(r!=null&&r.queryParams){const s=new URLSearchParams(r.queryParams);i.push(s.toString())}return r!=null&&r.skipBrowserRedirect&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${i.join("&")}`}async _unenroll(t){try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;return s?{data:null,error:s}:await $(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(M(n))return{data:null,error:n};throw n}}async _enroll(t){try{return await this._useSession(async n=>{var r,i;const{data:s,error:o}=n;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:l,error:u}=await $(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token});return u?{data:null,error:u}:(t.factorType==="totp"&&(!((i=l==null?void 0:l.totp)===null||i===void 0)&&i.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(n){if(M(n))return{data:null,error:n};throw n}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;if(s)return{data:null,error:s};const{data:o,error:a}=await $(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})})}catch(n){if(M(n))return{data:null,error:n};throw n}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;return s?{data:null,error:s}:await $(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(M(n))return{data:null,error:n};throw n}})}async _challengeAndVerify(t){const{data:n,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:n.id,code:t.code})}async _listFactors(){const{data:{user:t},error:n}=await this.getUser();if(n)return{data:null,error:n};const r=(t==null?void 0:t.factors)||[],i=r.filter(o=>o.factor_type==="totp"&&o.status==="verified"),s=r.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:r,totp:i,phone:s},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var n,r;const{data:{session:i},error:s}=t;if(s)return{data:null,error:s};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=tl(i.access_token);let a=null;o.aal&&(a=o.aal);let l=a;((r=(n=i.user.factors)===null||n===void 0?void 0:n.filter(d=>d.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(l="aal2");const c=o.amr||[];return{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:c},error:null}}))}async fetchJwk(t,n={keys:[]}){let r=n.keys.find(o=>o.kid===t);if(r||(r=this.jwks.keys.find(o=>o.kid===t),r&&this.jwks_cached_at+e_>Date.now()))return r;const{data:i,error:s}=await $(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(s)throw s;if(!i.keys||i.keys.length===0)throw new Lr("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),r=i.keys.find(o=>o.kid===t),!r)throw new Lr("No matching signing key found in JWKS");return r}async getClaims(t,n={keys:[]}){try{let r=t;if(!r){const{data:f,error:y}=await this.getSession();if(y||!f.session)return{data:null,error:y};r=f.session.access_token}const{header:i,payload:s,signature:o,raw:{header:a,payload:l}}=tl(r);if(S_(s.exp),!i.kid||i.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:f}=await this.getUser(r);if(f)throw f;return{data:{claims:s,header:i,signature:o},error:null}}const u=__(i.alg),c=await this.fetchJwk(i.kid,n),d=await crypto.subtle.importKey("jwk",c,u,!0,["verify"]);if(!await crypto.subtle.verify(u,d,o,a_(`${a}.${l}`)))throw new Lr("Invalid JWT signature");return{data:{claims:s,header:i,signature:o},error:null}}catch(r){if(M(r))return{data:null,error:r};throw r}}}Fr.nextInstanceID=0;const $_=Dc,tg=Fr;class V_ extends tg{constructor(t){super(t)}}var z_=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class ng{constructor(t,n,r){var i,s,o;if(this.supabaseUrl=t,this.supabaseKey=n,!t)throw new Error("supabaseUrl is required.");if(!n)throw new Error("supabaseKey is required.");const a=qS(t),l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const u=`sb-${l.hostname.split(".")[0]}-auth-token`,c={db:zS,realtime:US,auth:Object.assign(Object.assign({},BS),{storageKey:u}),global:VS},d=JS(r??{},c);this.storageKey=(i=d.auth.storageKey)!==null&&i!==void 0?i:"",this.headers=(s=d.global.headers)!==null&&s!==void 0?s:{},d.accessToken?(this.accessToken=d.accessToken,this.auth=new Proxy({},{get:(h,f)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(f)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=d.auth)!==null&&o!==void 0?o:{},this.headers,d.global.fetch),this.fetch=KS(n,this._getAccessToken.bind(this),d.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},d.realtime)),this.rest=new aS(new URL("rest/v1",l).href,{headers:this.headers,schema:d.db.schema,fetch:this.fetch}),d.accessToken||this._listenForAuthEvents()}get functions(){return new Dx(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new MS(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,n={},r={}){return this.rest.rpc(t,n,r)}channel(t,n={config:{}}){return this.realtime.channel(t,n)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,n;return z_(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(n=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,storageKey:s,flowType:o,lock:a,debug:l},u,c){const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new V_({url:this.authUrl.href,headers:Object.assign(Object.assign({},d),u),storageKey:s,autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,flowType:o,lock:a,debug:l,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new Bm(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((n,r)=>{this._handleTokenChanged(n,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(t,n,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),n=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const rg=(e,t,n)=>new ng(e,t,n),dT=Object.freeze(Object.defineProperty({__proto__:null,AuthAdminApi:$_,AuthApiError:Km,AuthClient:tg,AuthError:fa,AuthImplicitGrantRedirectError:yi,AuthInvalidCredentialsError:vi,AuthInvalidJwtError:Lr,AuthInvalidTokenResponseError:gi,AuthPKCEGrantCodeExchangeError:Su,AuthRetryableFetchError:Ro,AuthSessionMissingError:Tt,AuthUnknownError:Mc,AuthWeakPasswordError:_u,CustomAuthError:Ft,get FunctionRegion(){return jo},FunctionsError:sa,FunctionsFetchError:km,FunctionsHttpError:Em,FunctionsRelayError:Cm,GoTrueAdminApi:Dc,GoTrueClient:Fr,NavigatorLockAcquireTimeoutError:Zm,PostgrestError:lS,REALTIME_CHANNEL_STATES:_S,get REALTIME_LISTEN_TYPES(){return gu},get REALTIME_POSTGRES_CHANGES_LISTEN_EVENT(){return mu},get REALTIME_PRESENCE_LISTEN_EVENTS(){return pu},get REALTIME_SUBSCRIBE_STATES(){return vt},RealtimeChannel:ha,RealtimeClient:Bm,RealtimePresence:Or,SIGN_OUT_SCOPES:Zs,SupabaseClient:ng,createClient:rg,isAuthApiError:Gm,isAuthError:M,isAuthImplicitGrantRedirectError:Jm,isAuthRetryableFetchError:Xs,isAuthSessionMissingError:qm,isAuthWeakPasswordError:t_,lockInternals:Nn,navigatorLock:eg,processLock:M_},Symbol.toStringTag,{value:"Module"})),Ns=new Map,B_=1e3,U_=3e4,F_=5,Kn=_m((e,t)=>({isOnline:navigator.onLine,isBackendAvailable:!1,lastChecked:null,retryCount:0,isChecking:!1,checkConnection:async()=>{const n=t();if(n.isChecking)return n.isBackendAvailable;e({isChecking:!0});try{if(!navigator.onLine)return e({isOnline:!1,isBackendAvailable:!1,lastChecked:new Date,isChecking:!1}),!1;const r=new AbortController,i=setTimeout(()=>r.abort(),5e3),s=await fetch("/api/health",{method:"GET",signal:r.signal,headers:{"Cache-Control":"no-cache"}});clearTimeout(i);const o=s.ok;return e({isOnline:!0,isBackendAvailable:o,lastChecked:new Date,retryCount:o?0:n.retryCount+1,isChecking:!1}),o}catch{return e({isOnline:navigator.onLine,isBackendAvailable:!1,lastChecked:new Date,retryCount:n.retryCount+1,isChecking:!1}),!1}},setOffline:()=>e({isOnline:!1,isBackendAvailable:!1}),setOnline:()=>e({isOnline:!0}),reset:()=>e({isOnline:navigator.onLine,isBackendAvailable:!1,lastChecked:null,retryCount:0,isChecking:!1})})),wi=async(e,t={},n=!0)=>{const{checkConnection:r,isBackendAvailable:i,retryCount:s}=Kn.getState(),o=`${t.method||"GET"}-${e}-${JSON.stringify(t.body||{})}`;if(Ns.has(o))return Ns.get(o);const l=(async()=>{try{if(!i&&!await r())throw new Error("Backend server is not available");const u=new AbortController,c=setTimeout(()=>u.abort(),1e4),d=await fetch(e,{...t,signal:u.signal});return clearTimeout(c),d.ok&&!i&&Kn.getState().checkConnection(),d}catch(u){if(Kn.getState().setOffline(),n&&s<F_){const c=Math.min(B_*Math.pow(2,s),U_);return await new Promise(d=>setTimeout(d,c)),wi(e,t,!1)}throw u}finally{Ns.delete(o)}})();return Ns.set(o,l),l},H_=()=>{const{checkConnection:e,setOnline:t,setOffline:n}=Kn.getState();window.addEventListener("online",()=>{t(),e()}),window.addEventListener("offline",n);const r=()=>{setInterval(()=>{const{isBackendAvailable:i,isChecking:s}=Kn.getState();!i&&!s&&e()},3e4)};e().then(()=>{r()})},W_="https://jpvbtrzvbpyzgtpvltss.supabase.co",K_="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI",Bh=rg(W_,K_);class G_{setToken(t,n=!0){localStorage.removeItem("auth_token"),sessionStorage.removeItem("auth_token"),n?localStorage.setItem("auth_token",t):sessionStorage.setItem("auth_token",t)}getToken(){return localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}clearToken(){localStorage.removeItem("auth_token"),sessionStorage.removeItem("auth_token")}async signUp(t,n,r){try{const s=await(await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n,name:r})})).json();return s.success&&s.token&&this.setToken(s.token,!0),s}catch{return{success:!1,error:"Network error during signup"}}}async signIn(t,n,r=!0){try{const s=await(await wi("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n})})).json();return s.success&&s.token&&this.setToken(s.token,r),s}catch{return{success:!1,error:"Network error during login"}}}async signOut(){try{const t=this.getToken();t&&await wi("/api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${t}`}})}finally{this.clearToken()}}async getCurrentUser(){try{const t=this.getToken();if(!t)return null;const n=await wi("/api/auth/user",{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!n.ok)return n.status===401&&(console.log("Token expired or invalid, clearing..."),this.clearToken()),null;const r=await n.json();return r.success&&r.data?r.data:null}catch(t){return console.error("getCurrentUser error:",t),null}}isAuthenticated(){return!!this.getToken()}async signInWithGoogle(){try{const{error:t}=await Bh.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});return t?{error:t.message}:{}}catch{return{error:"Failed to sign in with Google"}}}async handleOAuthCallback(){var t,n;try{const{data:r,error:i}=await Bh.auth.getSession();if(i)return{success:!1,error:i.message};if(r.session){this.setToken(r.session.access_token,!0);const s=r.session.user;if(s){const a=await(await wi("/api/auth/oauth-user",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r.session.access_token}`},body:JSON.stringify({id:s.id,email:s.email,name:((t=s.user_metadata)==null?void 0:t.full_name)||((n=s.user_metadata)==null?void 0:n.name),provider:"google"})})).json();return a.success?{success:!0,user:a.user}:{success:!1,error:a.error||"Failed to create user profile"}}}return{success:!1,error:"No session found"}}catch{return{success:!1,error:"Failed to handle OAuth callback"}}}}const _t=new G_,Zr=_m((e,t)=>({user:null,isLoading:!0,isAuthenticated:!1,login:async(n,r,i=!0)=>{e({isLoading:!0});try{const s=await _t.signIn(n,r,i);return s.success&&s.user?(e({user:s.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:s.error||"Login failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},signup:async(n,r,i)=>{e({isLoading:!0});try{const s=await _t.signUp(n,r,i);return s.success&&s.user?(e({user:s.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:s.error||"Signup failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},signInWithGoogle:async()=>{e({isLoading:!0});try{const n=await _t.signInWithGoogle();return n.error?(e({isLoading:!1}),{success:!1,error:n.error}):{success:!0}}catch{return e({isLoading:!1}),{success:!1,error:"Failed to initiate Google sign in"}}},handleOAuthCallback:async()=>{e({isLoading:!0});try{const n=await _t.handleOAuthCallback();return n.success&&n.user?(e({user:n.user,isAuthenticated:!0,isLoading:!1}),{success:!0,user:n.user}):(e({isLoading:!1}),{success:!1,error:n.error||"OAuth callback failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error during OAuth callback"}}},logout:async()=>{e({isLoading:!0});try{await _t.signOut()}finally{e({user:null,isAuthenticated:!1,isLoading:!1})}},checkAuth:async()=>{e({isLoading:!0});try{if(!_t.isAuthenticated()){e({user:null,isAuthenticated:!1,isLoading:!1});return}const r=await _t.getCurrentUser();r?e({user:r,isAuthenticated:!0,isLoading:!1}):(await _t.signOut(),e({user:null,isAuthenticated:!1,isLoading:!1}))}catch(n){console.error("Auth check failed:",n),await _t.signOut(),e({user:null,isAuthenticated:!1,isLoading:!1})}},updateUser:n=>{const{user:r}=t();r&&e({user:{...r,...n}})}})),ig=S.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),ma=S.createContext({}),ga=S.createContext(null),va=typeof document<"u",Vc=va?S.useLayoutEffect:S.useEffect,sg=S.createContext({strict:!1}),zc=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),q_="framerAppearId",og="data-"+zc(q_);function J_(e,t,n,r){const{visualElement:i}=S.useContext(ma),s=S.useContext(sg),o=S.useContext(ga),a=S.useContext(ig).reducedMotion,l=S.useRef();r=r||s.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:i,props:n,presenceContext:o,blockInitialAnimation:o?o.initial===!1:!1,reducedMotionConfig:a}));const u=l.current;S.useInsertionEffect(()=>{u&&u.update(n,o)});const c=S.useRef(!!(n[og]&&!window.HandoffComplete));return Vc(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),S.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function Sr(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Q_(e,t,n){return S.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Sr(n)&&(n.current=r))},[t])}function ts(e){return typeof e=="string"||Array.isArray(e)}function ya(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Bc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Uc=["initial",...Bc];function wa(e){return ya(e.animate)||Uc.some(t=>ts(e[t]))}function ag(e){return!!(wa(e)||e.variants)}function Y_(e,t){if(wa(e)){const{initial:n,animate:r}=e;return{initial:n===!1||ts(n)?n:void 0,animate:ts(r)?r:void 0}}return e.inherit!==!1?t:{}}function X_(e){const{initial:t,animate:n}=Y_(e,S.useContext(ma));return S.useMemo(()=>({initial:t,animate:n}),[Uh(t),Uh(n)])}function Uh(e){return Array.isArray(e)?e.join(" "):e}const Fh={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ns={};for(const e in Fh)ns[e]={isEnabled:t=>Fh[e].some(n=>!!t[n])};function Z_(e){for(const t in e)ns[t]={...ns[t],...e[t]}}const Fc=S.createContext({}),lg=S.createContext({}),ek=Symbol.for("motionComponentSymbol");function tk({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&Z_(e);function s(a,l){let u;const c={...S.useContext(ig),...a,layoutId:nk(a)},{isStatic:d}=c,h=X_(a),f=r(a,d);if(!d&&va){h.visualElement=J_(i,f,c,t);const y=S.useContext(lg),w=S.useContext(sg).strict;h.visualElement&&(u=h.visualElement.loadFeatures(c,w,e,y))}return S.createElement(ma.Provider,{value:h},u&&h.visualElement?S.createElement(u,{visualElement:h.visualElement,...c}):null,n(i,a,Q_(f,h.visualElement,l),f,d,h.visualElement))}const o=S.forwardRef(s);return o[ek]=i,o}function nk({layoutId:e}){const t=S.useContext(Fc).id;return t&&e!==void 0?t+"-"+e:e}function rk(e){function t(r,i={}){return tk(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const ik=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Hc(e){return typeof e!="string"||e.includes("-")?!1:!!(ik.indexOf(e)>-1||/[A-Z]/.test(e))}const Lo={};function sk(e){Object.assign(Lo,e)}const ds=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],tr=new Set(ds);function ug(e,{layout:t,layoutId:n}){return tr.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Lo[e]||e==="opacity")}const $e=e=>!!(e&&e.getVelocity),ok={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ak=ds.length;function lk(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let s="";for(let o=0;o<ak;o++){const a=ds[o];if(e[a]!==void 0){const l=ok[a]||a;s+=`${l}(${e[a]}) `}}return t&&!e.z&&(s+="translateZ(0)"),s=s.trim(),i?s=i(e,r?"":s):n&&r&&(s="none"),s}const cg=e=>t=>typeof t=="string"&&t.startsWith(e),dg=cg("--"),ku=cg("var(--"),uk=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,ck=(e,t)=>t&&typeof e=="number"?t.transform(e):e,yn=(e,t,n)=>Math.min(Math.max(n,e),t),nr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Oi={...nr,transform:e=>yn(0,1,e)},Ms={...nr,default:1},Li=e=>Math.round(e*1e5)/1e5,xa=/(-)?([\d]*\.?[\d])+/g,hg=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,dk=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function hs(e){return typeof e=="string"}const fs=e=>({test:t=>hs(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Kt=fs("deg"),St=fs("%"),R=fs("px"),hk=fs("vh"),fk=fs("vw"),Hh={...St,parse:e=>St.parse(e)/100,transform:e=>St.transform(e*100)},Wh={...nr,transform:Math.round},fg={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,size:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,rotate:Kt,rotateX:Kt,rotateY:Kt,rotateZ:Kt,scale:Ms,scaleX:Ms,scaleY:Ms,scaleZ:Ms,skew:Kt,skewX:Kt,skewY:Kt,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:Oi,originX:Hh,originY:Hh,originZ:R,zIndex:Wh,fillOpacity:Oi,strokeOpacity:Oi,numOctaves:Wh};function Wc(e,t,n,r){const{style:i,vars:s,transform:o,transformOrigin:a}=e;let l=!1,u=!1,c=!0;for(const d in t){const h=t[d];if(dg(d)){s[d]=h;continue}const f=fg[d],y=ck(h,f);if(tr.has(d)){if(l=!0,o[d]=y,!c)continue;h!==(f.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,a[d]=y):i[d]=y}if(t.transform||(l||r?i.transform=lk(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:d="50%",originY:h="50%",originZ:f=0}=a;i.transformOrigin=`${d} ${h} ${f}`}}const Kc=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function pg(e,t,n){for(const r in t)!$e(t[r])&&!ug(r,n)&&(e[r]=t[r])}function pk({transformTemplate:e},t,n){return S.useMemo(()=>{const r=Kc();return Wc(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function mk(e,t,n){const r=e.style||{},i={};return pg(i,r,e),Object.assign(i,pk(e,t,n)),e.transformValues?e.transformValues(i):i}function gk(e,t,n){const r={},i=mk(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const vk=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Io(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||vk.has(e)}let mg=e=>!Io(e);function yk(e){e&&(mg=t=>t.startsWith("on")?!Io(t):e(t))}try{yk(require("@emotion/is-prop-valid").default)}catch{}function wk(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(mg(i)||n===!0&&Io(i)||!t&&!Io(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Kh(e,t,n){return typeof e=="string"?e:R.transform(t+n*e)}function xk(e,t,n){const r=Kh(t,e.x,e.width),i=Kh(n,e.y,e.height);return`${r} ${i}`}const Sk={offset:"stroke-dashoffset",array:"stroke-dasharray"},_k={offset:"strokeDashoffset",array:"strokeDasharray"};function kk(e,t,n=1,r=0,i=!0){e.pathLength=1;const s=i?Sk:_k;e[s.offset]=R.transform(-r);const o=R.transform(t),a=R.transform(n);e[s.array]=`${o} ${a}`}function Gc(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,d,h){if(Wc(e,u,c,h),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:y,dimensions:w}=e;f.transform&&(w&&(y.transform=f.transform),delete f.transform),w&&(i!==void 0||s!==void 0||y.transform)&&(y.transformOrigin=xk(w,i!==void 0?i:.5,s!==void 0?s:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),o!==void 0&&kk(f,o,a,l,!1)}const gg=()=>({...Kc(),attrs:{}}),qc=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Ck(e,t,n,r){const i=S.useMemo(()=>{const s=gg();return Gc(s,t,{enableHardwareAcceleration:!1},qc(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};pg(s,e.style,e),i.style={...s,...i.style}}return i}function Ek(e=!1){return(n,r,i,{latestValues:s},o)=>{const l=(Hc(n)?Ck:gk)(r,s,o,n),c={...wk(r,typeof n=="string",e),...l,ref:i},{children:d}=r,h=S.useMemo(()=>$e(d)?d.get():d,[d]);return S.createElement(n,{...c,children:h})}}function vg(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const s in n)e.style.setProperty(s,n[s])}const yg=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function wg(e,t,n,r){vg(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(yg.has(i)?i:zc(i),t.attrs[i])}function Jc(e,t){const{style:n}=e,r={};for(const i in n)($e(n[i])||t.style&&$e(t.style[i])||ug(i,e))&&(r[i]=n[i]);return r}function xg(e,t){const n=Jc(e,t);for(const r in e)if($e(e[r])||$e(t[r])){const i=ds.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function Qc(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function Sg(e){const t=S.useRef(null);return t.current===null&&(t.current=e()),t.current}const No=e=>Array.isArray(e),bk=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Tk=e=>No(e)?e[e.length-1]||0:e;function eo(e){const t=$e(e)?e.get():e;return bk(t)?t.toValue():t}function Pk({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,s){const o={latestValues:jk(r,i,s,e),renderState:t()};return n&&(o.mount=a=>n(r,a,o)),o}const _g=e=>(t,n)=>{const r=S.useContext(ma),i=S.useContext(ga),s=()=>Pk(e,t,r,i);return n?s():Sg(s)};function jk(e,t,n,r){const i={},s=r(e,{});for(const h in s)i[h]=eo(s[h]);let{initial:o,animate:a}=e;const l=wa(e),u=ag(e);t&&u&&!l&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||o===!1;const d=c?a:o;return d&&typeof d!="boolean"&&!ya(d)&&(Array.isArray(d)?d:[d]).forEach(f=>{const y=Qc(e,f);if(!y)return;const{transitionEnd:w,transition:x,...v}=y;for(const p in v){let m=v[p];if(Array.isArray(m)){const _=c?m.length-1:0;m=m[_]}m!==null&&(i[p]=m)}for(const p in w)i[p]=w[p]}),i}const te=e=>e;class Gh{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function Ak(e){let t=new Gh,n=new Gh,r=0,i=!1,s=!1;const o=new WeakSet,a={schedule:(l,u=!1,c=!1)=>{const d=c&&i,h=d?t:n;return u&&o.add(l),h.add(l)&&d&&i&&(r=t.order.length),l},cancel:l=>{n.remove(l),o.delete(l)},process:l=>{if(i){s=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(l),o.has(c)&&(a.schedule(c),e())}i=!1,s&&(s=!1,a.process(l))}};return a}const Ds=["prepare","read","update","preRender","render","postRender"],Rk=40;function Ok(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},s=Ds.reduce((d,h)=>(d[h]=Ak(()=>n=!0),d),{}),o=d=>s[d].process(i),a=()=>{const d=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(d-i.timestamp,Rk),1),i.timestamp=d,i.isProcessing=!0,Ds.forEach(o),i.isProcessing=!1,n&&t&&(r=!1,e(a))},l=()=>{n=!0,r=!0,i.isProcessing||e(a)};return{schedule:Ds.reduce((d,h)=>{const f=s[h];return d[h]=(y,w=!1,x=!1)=>(n||l(),f.schedule(y,w,x)),d},{}),cancel:d=>Ds.forEach(h=>s[h].cancel(d)),state:i,steps:s}}const{schedule:H,cancel:Vt,state:Se,steps:nl}=Ok(typeof requestAnimationFrame<"u"?requestAnimationFrame:te,!0),Lk={useVisualState:_g({scrapeMotionValuesFromProps:xg,createRenderState:gg,onMount:(e,t,{renderState:n,latestValues:r})=>{H.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),H.render(()=>{Gc(n,r,{enableHardwareAcceleration:!1},qc(t.tagName),e.transformTemplate),wg(t,n)})}})},Ik={useVisualState:_g({scrapeMotionValuesFromProps:Jc,createRenderState:Kc})};function Nk(e,{forwardMotionProps:t=!1},n,r){return{...Hc(e)?Lk:Ik,preloadedFeatures:n,useRender:Ek(t),createVisualElement:r,Component:e}}function Rt(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const kg=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function Sa(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const Mk=e=>t=>kg(t)&&e(t,Sa(t));function Lt(e,t,n,r){return Rt(e,t,Mk(n),r)}const Dk=(e,t)=>n=>t(e(n)),fn=(...e)=>e.reduce(Dk);function Cg(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const qh=Cg("dragHorizontal"),Jh=Cg("dragVertical");function Eg(e){let t=!1;if(e==="y")t=Jh();else if(e==="x")t=qh();else{const n=qh(),r=Jh();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function bg(){const e=Eg(!0);return e?(e(),!1):!0}class bn{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Qh(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(s,o)=>{if(s.pointerType==="touch"||bg())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&H.update(()=>a[r](s,o))};return Lt(e.current,n,i,{passive:!e.getProps()[r]})}class $k extends bn{mount(){this.unmount=fn(Qh(this.node,!0),Qh(this.node,!1))}unmount(){}}class Vk extends bn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=fn(Rt(this.node.current,"focus",()=>this.onFocus()),Rt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Tg=(e,t)=>t?e===t?!0:Tg(e,t.parentElement):!1;function rl(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,Sa(n))}class zk extends bn{constructor(){super(...arguments),this.removeStartListeners=te,this.removeEndListeners=te,this.removeAccessibleListeners=te,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),s=Lt(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:d}=this.node.getProps();H.update(()=>{!d&&!Tg(this.node.current,a.target)?c&&c(a,l):u&&u(a,l)})},{passive:!(r.onTap||r.onPointerUp)}),o=Lt(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=fn(s,o),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=s=>{if(s.key!=="Enter"||this.isPressing)return;const o=a=>{a.key!=="Enter"||!this.checkPressEnd()||rl("up",(l,u)=>{const{onTap:c}=this.node.getProps();c&&H.update(()=>c(l,u))})};this.removeEndListeners(),this.removeEndListeners=Rt(this.node.current,"keyup",o),rl("down",(a,l)=>{this.startPress(a,l)})},n=Rt(this.node.current,"keydown",t),r=()=>{this.isPressing&&rl("cancel",(s,o)=>this.cancelPress(s,o))},i=Rt(this.node.current,"blur",r);this.removeAccessibleListeners=fn(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&H.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!bg()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&H.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=Lt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Rt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=fn(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Cu=new WeakMap,il=new WeakMap,Bk=e=>{const t=Cu.get(e.target);t&&t(e)},Uk=e=>{e.forEach(Bk)};function Fk({root:e,...t}){const n=e||document;il.has(n)||il.set(n,{});const r=il.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(Uk,{root:e,...t})),r[i]}function Hk(e,t,n){const r=Fk(t);return Cu.set(e,n),r.observe(e),()=>{Cu.delete(e),r.unobserve(e)}}const Wk={some:0,all:1};class Kk extends bn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:s}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:Wk[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),h=u?c:d;h&&h(l)};return Hk(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Gk(t,n))&&this.startObserver()}unmount(){}}function Gk({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const qk={inView:{Feature:Kk},tap:{Feature:zk},focus:{Feature:Vk},hover:{Feature:$k}};function Pg(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Jk(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function Qk(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function _a(e,t,n){const r=e.getProps();return Qc(r,t,n!==void 0?n:r.custom,Jk(e),Qk(e))}let Yk=te,Yc=te;const pn=e=>e*1e3,It=e=>e/1e3,Xk={current:!1},jg=e=>Array.isArray(e)&&typeof e[0]=="number";function Ag(e){return!!(!e||typeof e=="string"&&Rg[e]||jg(e)||Array.isArray(e)&&e.every(Ag))}const xi=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Rg={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:xi([0,.65,.55,1]),circOut:xi([.55,0,1,.45]),backIn:xi([.31,.01,.66,-.59]),backOut:xi([.33,1.53,.69,.99])};function Og(e){if(e)return jg(e)?xi(e):Array.isArray(e)?e.map(Og):Rg[e]}function Zk(e,t,n,{delay:r=0,duration:i,repeat:s=0,repeatType:o="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=Og(a);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:o==="reverse"?"alternate":"normal"})}function e2(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Lg=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,t2=1e-7,n2=12;function r2(e,t,n,r,i){let s,o,a=0;do o=t+(n-t)/2,s=Lg(o,r,i)-e,s>0?n=o:t=o;while(Math.abs(s)>t2&&++a<n2);return o}function ps(e,t,n,r){if(e===t&&n===r)return te;const i=s=>r2(s,0,1,e,n);return s=>s===0||s===1?s:Lg(i(s),t,r)}const i2=ps(.42,0,1,1),s2=ps(0,0,.58,1),Ig=ps(.42,0,.58,1),o2=e=>Array.isArray(e)&&typeof e[0]!="number",Ng=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Mg=e=>t=>1-e(1-t),Xc=e=>1-Math.sin(Math.acos(e)),Dg=Mg(Xc),a2=Ng(Xc),$g=ps(.33,1.53,.69,.99),Zc=Mg($g),l2=Ng(Zc),u2=e=>(e*=2)<1?.5*Zc(e):.5*(2-Math.pow(2,-10*(e-1))),c2={linear:te,easeIn:i2,easeInOut:Ig,easeOut:s2,circIn:Xc,circInOut:a2,circOut:Dg,backIn:Zc,backInOut:l2,backOut:$g,anticipate:u2},Yh=e=>{if(Array.isArray(e)){Yc(e.length===4);const[t,n,r,i]=e;return ps(t,n,r,i)}else if(typeof e=="string")return c2[e];return e},ed=(e,t)=>n=>!!(hs(n)&&dk.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Vg=(e,t,n)=>r=>{if(!hs(r))return r;const[i,s,o,a]=r.match(xa);return{[e]:parseFloat(i),[t]:parseFloat(s),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},d2=e=>yn(0,255,e),sl={...nr,transform:e=>Math.round(d2(e))},Fn={test:ed("rgb","red"),parse:Vg("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+sl.transform(e)+", "+sl.transform(t)+", "+sl.transform(n)+", "+Li(Oi.transform(r))+")"};function h2(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Eu={test:ed("#"),parse:h2,transform:Fn.transform},_r={test:ed("hsl","hue"),parse:Vg("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+St.transform(Li(t))+", "+St.transform(Li(n))+", "+Li(Oi.transform(r))+")"},Ee={test:e=>Fn.test(e)||Eu.test(e)||_r.test(e),parse:e=>Fn.test(e)?Fn.parse(e):_r.test(e)?_r.parse(e):Eu.parse(e),transform:e=>hs(e)?e:e.hasOwnProperty("red")?Fn.transform(e):_r.transform(e)},Y=(e,t,n)=>-n*e+n*t+e;function ol(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function f2({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,s=0,o=0;if(!t)i=s=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=ol(l,a,e+1/3),s=ol(l,a,e),o=ol(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(s*255),blue:Math.round(o*255),alpha:r}}const al=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},p2=[Eu,Fn,_r],m2=e=>p2.find(t=>t.test(e));function Xh(e){const t=m2(e);let n=t.parse(e);return t===_r&&(n=f2(n)),n}const zg=(e,t)=>{const n=Xh(e),r=Xh(t),i={...n};return s=>(i.red=al(n.red,r.red,s),i.green=al(n.green,r.green,s),i.blue=al(n.blue,r.blue,s),i.alpha=Y(n.alpha,r.alpha,s),Fn.transform(i))};function g2(e){var t,n;return isNaN(e)&&hs(e)&&(((t=e.match(xa))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(hg))===null||n===void 0?void 0:n.length)||0)>0}const Bg={regex:uk,countKey:"Vars",token:"${v}",parse:te},Ug={regex:hg,countKey:"Colors",token:"${c}",parse:Ee.parse},Fg={regex:xa,countKey:"Numbers",token:"${n}",parse:nr.parse};function ll(e,{regex:t,countKey:n,token:r,parse:i}){const s=e.tokenised.match(t);s&&(e["num"+n]=s.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...s.map(i)))}function Mo(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&ll(n,Bg),ll(n,Ug),ll(n,Fg),n}function Hg(e){return Mo(e).values}function Wg(e){const{values:t,numColors:n,numVars:r,tokenised:i}=Mo(e),s=t.length;return o=>{let a=i;for(let l=0;l<s;l++)l<r?a=a.replace(Bg.token,o[l]):l<r+n?a=a.replace(Ug.token,Ee.transform(o[l])):a=a.replace(Fg.token,Li(o[l]));return a}}const v2=e=>typeof e=="number"?0:e;function y2(e){const t=Hg(e);return Wg(e)(t.map(v2))}const wn={test:g2,parse:Hg,createTransformer:Wg,getAnimatableNone:y2},Kg=(e,t)=>n=>`${n>0?t:e}`;function Gg(e,t){return typeof e=="number"?n=>Y(e,t,n):Ee.test(e)?zg(e,t):e.startsWith("var(")?Kg(e,t):Jg(e,t)}const qg=(e,t)=>{const n=[...e],r=n.length,i=e.map((s,o)=>Gg(s,t[o]));return s=>{for(let o=0;o<r;o++)n[o]=i[o](s);return n}},w2=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Gg(e[i],t[i]));return i=>{for(const s in r)n[s]=r[s](i);return n}},Jg=(e,t)=>{const n=wn.createTransformer(t),r=Mo(e),i=Mo(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?fn(qg(r.values,i.values),n):Kg(e,t)},rs=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Zh=(e,t)=>n=>Y(e,t,n);function x2(e){return typeof e=="number"?Zh:typeof e=="string"?Ee.test(e)?zg:Jg:Array.isArray(e)?qg:typeof e=="object"?w2:Zh}function S2(e,t,n){const r=[],i=n||x2(e[0]),s=e.length-1;for(let o=0;o<s;o++){let a=i(e[o],e[o+1]);if(t){const l=Array.isArray(t)?t[o]||te:t;a=fn(l,a)}r.push(a)}return r}function Qg(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const s=e.length;if(Yc(s===t.length),s===1)return()=>t[0];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=S2(t,r,i),a=o.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const d=rs(e[c],e[c+1],u);return o[c](d)};return n?u=>l(yn(e[0],e[s-1],u)):l}function _2(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=rs(0,t,r);e.push(Y(n,1,i))}}function k2(e){const t=[0];return _2(t,e.length-1),t}function C2(e,t){return e.map(n=>n*t)}function E2(e,t){return e.map(()=>t||Ig).splice(0,e.length-1)}function Do({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=o2(r)?r.map(Yh):Yh(r),s={done:!1,value:t[0]},o=C2(n&&n.length===t.length?n:k2(t),e),a=Qg(o,t,{ease:Array.isArray(i)?i:E2(t,i)});return{calculatedDuration:e,next:l=>(s.value=a(l),s.done=l>=e,s)}}function Yg(e,t){return t?e*(1e3/t):0}const b2=5;function Xg(e,t,n){const r=Math.max(t-b2,0);return Yg(n-e(r),t-r)}const ul=.001,T2=.01,ef=10,P2=.05,j2=1;function A2({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,s;Yk(e<=pn(ef));let o=1-t;o=yn(P2,j2,o),e=yn(T2,ef,It(e)),o<1?(i=u=>{const c=u*o,d=c*e,h=c-n,f=bu(u,o),y=Math.exp(-d);return ul-h/f*y},s=u=>{const d=u*o*e,h=d*n+n,f=Math.pow(o,2)*Math.pow(u,2)*e,y=Math.exp(-d),w=bu(Math.pow(u,2),o);return(-i(u)+ul>0?-1:1)*((h-f)*y)/w}):(i=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-ul+c*d},s=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const a=5/e,l=O2(i,s,a);if(e=pn(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const R2=12;function O2(e,t,n){let r=n;for(let i=1;i<R2;i++)r=r-e(r)/t(r);return r}function bu(e,t){return e*Math.sqrt(1-t*t)}const L2=["duration","bounce"],I2=["stiffness","damping","mass"];function tf(e,t){return t.some(n=>e[n]!==void 0)}function N2(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!tf(e,I2)&&tf(e,L2)){const n=A2(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function Zg({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],s=e[e.length-1],o={done:!1,value:i},{stiffness:a,damping:l,mass:u,duration:c,velocity:d,isResolvedFromDuration:h}=N2({...r,velocity:-It(r.velocity||0)}),f=d||0,y=l/(2*Math.sqrt(a*u)),w=s-i,x=It(Math.sqrt(a/u)),v=Math.abs(w)<5;n||(n=v?.01:2),t||(t=v?.005:.5);let p;if(y<1){const m=bu(x,y);p=_=>{const k=Math.exp(-y*x*_);return s-k*((f+y*x*w)/m*Math.sin(m*_)+w*Math.cos(m*_))}}else if(y===1)p=m=>s-Math.exp(-x*m)*(w+(f+x*w)*m);else{const m=x*Math.sqrt(y*y-1);p=_=>{const k=Math.exp(-y*x*_),C=Math.min(m*_,300);return s-k*((f+y*x*w)*Math.sinh(C)+m*w*Math.cosh(C))/m}}return{calculatedDuration:h&&c||null,next:m=>{const _=p(m);if(h)o.done=m>=c;else{let k=f;m!==0&&(y<1?k=Xg(p,m,_):k=0);const C=Math.abs(k)<=n,E=Math.abs(s-_)<=t;o.done=C&&E}return o.value=o.done?s:_,o}}}function nf({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],h={done:!1,value:d},f=b=>a!==void 0&&b<a||l!==void 0&&b>l,y=b=>a===void 0?l:l===void 0||Math.abs(a-b)<Math.abs(l-b)?a:l;let w=n*t;const x=d+w,v=o===void 0?x:o(x);v!==x&&(w=v-d);const p=b=>-w*Math.exp(-b/r),m=b=>v+p(b),_=b=>{const A=p(b),O=m(b);h.done=Math.abs(A)<=u,h.value=h.done?v:O};let k,C;const E=b=>{f(h.value)&&(k=b,C=Zg({keyframes:[h.value,y(h.value)],velocity:Xg(m,b,h.value),damping:i,stiffness:s,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:b=>{let A=!1;return!C&&k===void 0&&(A=!0,_(b),E(b)),k!==void 0&&b>k?C.next(b-k):(!A&&_(b),h)}}}const M2=e=>{const t=({timestamp:n})=>e(n);return{start:()=>H.update(t,!0),stop:()=>Vt(t),now:()=>Se.isProcessing?Se.timestamp:performance.now()}},rf=2e4;function sf(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<rf;)t+=n,r=e.next(t);return t>=rf?1/0:t}const D2={decay:nf,inertia:nf,tween:Do,keyframes:Do,spring:Zg};function $o({autoplay:e=!0,delay:t=0,driver:n=M2,keyframes:r,type:i="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:d,...h}){let f=1,y=!1,w,x;const v=()=>{x=new Promise(N=>{w=N})};v();let p;const m=D2[i]||Do;let _;m!==Do&&typeof r[0]!="number"&&(_=Qg([0,100],r,{clamp:!1}),r=[0,100]);const k=m({...h,keyframes:r});let C;a==="mirror"&&(C=m({...h,keyframes:[...r].reverse(),velocity:-(h.velocity||0)}));let E="idle",b=null,A=null,O=null;k.calculatedDuration===null&&s&&(k.calculatedDuration=sf(k));const{calculatedDuration:ue}=k;let pe=1/0,Ce=1/0;ue!==null&&(pe=ue+o,Ce=pe*(s+1)-o);let ce=0;const Ht=N=>{if(A===null)return;f>0&&(A=Math.min(A,N)),f<0&&(A=Math.min(N-Ce/f,A)),b!==null?ce=b:ce=Math.round(N-A)*f;const G=ce-t*(f>=0?1:-1),Tn=f>=0?G<0:G>Ce;ce=Math.max(G,0),E==="finished"&&b===null&&(ce=Ce);let pt=ce,rr=k;if(s){const ka=Math.min(ce,Ce)/pe;let ms=Math.floor(ka),jn=ka%1;!jn&&ka>=1&&(jn=1),jn===1&&ms--,ms=Math.min(ms,s+1),!!(ms%2)&&(a==="reverse"?(jn=1-jn,o&&(jn-=o/pe)):a==="mirror"&&(rr=C)),pt=yn(0,1,jn)*pe}const ze=Tn?{done:!1,value:r[0]}:rr.next(pt);_&&(ze.value=_(ze.value));let{done:Pn}=ze;!Tn&&ue!==null&&(Pn=f>=0?ce>=Ce:ce<=0);const Ov=b===null&&(E==="finished"||E==="running"&&Pn);return d&&d(ze.value),Ov&&P(),ze},ne=()=>{p&&p.stop(),p=void 0},Je=()=>{E="idle",ne(),w(),v(),A=O=null},P=()=>{E="finished",c&&c(),ne(),w()},L=()=>{if(y)return;p||(p=n(Ht));const N=p.now();l&&l(),b!==null?A=N-b:(!A||E==="finished")&&(A=N),E==="finished"&&v(),O=A,b=null,E="running",p.start()};e&&L();const D={then(N,G){return x.then(N,G)},get time(){return It(ce)},set time(N){N=pn(N),ce=N,b!==null||!p||f===0?b=N:A=p.now()-N/f},get duration(){const N=k.calculatedDuration===null?sf(k):k.calculatedDuration;return It(N)},get speed(){return f},set speed(N){N===f||!p||(f=N,D.time=It(ce))},get state(){return E},play:L,pause:()=>{E="paused",b=ce},stop:()=>{y=!0,E!=="idle"&&(E="idle",u&&u(),Je())},cancel:()=>{O!==null&&Ht(O),Je()},complete:()=>{E="finished"},sample:N=>(A=0,Ht(N))};return D}function $2(e){let t;return()=>(t===void 0&&(t=e()),t)}const V2=$2(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),z2=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),$s=10,B2=2e4,U2=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Ag(t.ease);function F2(e,t,{onUpdate:n,onComplete:r,...i}){if(!(V2()&&z2.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let o=!1,a,l,u=!1;const c=()=>{l=new Promise(m=>{a=m})};c();let{keyframes:d,duration:h=300,ease:f,times:y}=i;if(U2(t,i)){const m=$o({...i,repeat:0,delay:0});let _={done:!1,value:d[0]};const k=[];let C=0;for(;!_.done&&C<B2;)_=m.sample(C),k.push(_.value),C+=$s;y=void 0,d=k,h=C-$s,f="linear"}const w=Zk(e.owner.current,t,d,{...i,duration:h,ease:f,times:y}),x=()=>{u=!1,w.cancel()},v=()=>{u=!0,H.update(x),a(),c()};return w.onfinish=()=>{u||(e.set(e2(d,i)),r&&r(),v())},{then(m,_){return l.then(m,_)},attachTimeline(m){return w.timeline=m,w.onfinish=null,te},get time(){return It(w.currentTime||0)},set time(m){w.currentTime=pn(m)},get speed(){return w.playbackRate},set speed(m){w.playbackRate=m},get duration(){return It(h)},play:()=>{o||(w.play(),Vt(x))},pause:()=>w.pause(),stop:()=>{if(o=!0,w.playState==="idle")return;const{currentTime:m}=w;if(m){const _=$o({...i,autoplay:!1});e.setWithVelocity(_.sample(m-$s).value,_.sample(m).value,$s)}v()},complete:()=>{u||w.finish()},cancel:v}}function H2({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:te,pause:te,stop:te,then:s=>(s(),Promise.resolve()),cancel:te,complete:te});return t?$o({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const W2={type:"spring",stiffness:500,damping:25,restSpeed:10},K2=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),G2={type:"keyframes",duration:.8},q2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},J2=(e,{keyframes:t})=>t.length>2?G2:tr.has(e)?e.startsWith("scale")?K2(t[1]):W2:q2,Tu=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(wn.test(t)||t==="0")&&!t.startsWith("url(")),Q2=new Set(["brightness","contrast","saturate","opacity"]);function Y2(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(xa)||[];if(!r)return e;const i=n.replace(r,"");let s=Q2.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+i+")"}const X2=/([a-z-]*)\(.*?\)/g,Pu={...wn,getAnimatableNone:e=>{const t=e.match(X2);return t?t.map(Y2).join(" "):e}},Z2={...fg,color:Ee,backgroundColor:Ee,outlineColor:Ee,fill:Ee,stroke:Ee,borderColor:Ee,borderTopColor:Ee,borderRightColor:Ee,borderBottomColor:Ee,borderLeftColor:Ee,filter:Pu,WebkitFilter:Pu},td=e=>Z2[e];function ev(e,t){let n=td(e);return n!==Pu&&(n=wn),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const tv=e=>/^0[^.\s]+$/.test(e);function eC(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||tv(e)}function tC(e,t,n,r){const i=Tu(t,n);let s;Array.isArray(n)?s=[...n]:s=[null,n];const o=r.from!==void 0?r.from:e.get();let a;const l=[];for(let u=0;u<s.length;u++)s[u]===null&&(s[u]=u===0?o:s[u-1]),eC(s[u])&&l.push(u),typeof s[u]=="string"&&s[u]!=="none"&&s[u]!=="0"&&(a=s[u]);if(i&&l.length&&a)for(let u=0;u<l.length;u++){const c=l[u];s[c]=ev(t,a)}return s}function nC({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}function nd(e,t){return e[t]||e.default||e}const rC={skipAnimations:!1},rd=(e,t,n,r={})=>i=>{const s=nd(r,e)||{},o=s.delay||r.delay||0;let{elapsed:a=0}=r;a=a-pn(o);const l=tC(t,e,n,s),u=l[0],c=l[l.length-1],d=Tu(e,u),h=Tu(e,c);let f={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:y=>{t.set(y),s.onUpdate&&s.onUpdate(y)},onComplete:()=>{i(),s.onComplete&&s.onComplete()}};if(nC(s)||(f={...f,...J2(e,f)}),f.duration&&(f.duration=pn(f.duration)),f.repeatDelay&&(f.repeatDelay=pn(f.repeatDelay)),!d||!h||Xk.current||s.type===!1||rC.skipAnimations)return H2(f);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const y=F2(t,e,f);if(y)return y}return $o(f)};function Vo(e){return!!($e(e)&&e.add)}const nv=e=>/^\-?\d*\.?\d+$/.test(e);function id(e,t){e.indexOf(t)===-1&&e.push(t)}function sd(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class od{constructor(){this.subscriptions=[]}add(t){return id(this.subscriptions,t),()=>sd(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let s=0;s<i;s++){const o=this.subscriptions[s];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const iC=e=>!isNaN(parseFloat(e));class sC{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:s,timestamp:o}=Se;this.lastUpdated!==o&&(this.timeDelta=s,this.lastUpdated=o,H.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>H.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=iC(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new od);const r=this.events[t].add(n);return t==="change"?()=>{r(),H.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Yg(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Hr(e,t){return new sC(e,t)}const rv=e=>t=>t.test(e),oC={test:e=>e==="auto",parse:e=>e},iv=[nr,R,St,Kt,fk,hk,oC],ui=e=>iv.find(rv(e)),aC=[...iv,Ee,wn],lC=e=>aC.find(rv(e));function uC(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Hr(n))}function cC(e,t){const n=_a(e,t);let{transitionEnd:r={},transition:i={},...s}=n?e.makeTargetAnimatable(n,!1):{};s={...s,...r};for(const o in s){const a=Tk(s[o]);uC(e,o,a)}}function dC(e,t,n){var r,i;const s=Object.keys(t).filter(a=>!e.hasValue(a)),o=s.length;if(o)for(let a=0;a<o;a++){const l=s[a],u=t[l];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(i=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&i!==void 0?i:t[l]),c!=null&&(typeof c=="string"&&(nv(c)||tv(c))?c=parseFloat(c):!lC(c)&&wn.test(u)&&(c=ev(l,u)),e.addValue(l,Hr(c,{owner:e})),n[l]===void 0&&(n[l]=c),c!==null&&e.setBaseTarget(l,c))}}function hC(e,t){return t?(t[e]||t.default||t).from:void 0}function fC(e,t,n){const r={};for(const i in e){const s=hC(i,t);if(s!==void 0)r[i]=s;else{const o=n.getValue(i);o&&(r[i]=o.get())}}return r}function pC({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function mC(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function sv(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:o,...a}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(s=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const d in a){const h=e.getValue(d),f=a[d];if(!h||f===void 0||c&&pC(c,d))continue;const y={delay:n,elapsed:0,...nd(s||{},d)};if(window.HandoffAppearAnimations){const v=e.getProps()[og];if(v){const p=window.HandoffAppearAnimations(v,d,h,H);p!==null&&(y.elapsed=p,y.isHandoff=!0)}}let w=!y.isHandoff&&!mC(h,f);if(y.type==="spring"&&(h.getVelocity()||y.velocity)&&(w=!1),h.animation&&(w=!1),w)continue;h.start(rd(d,h,f,e.shouldReduceMotion&&tr.has(d)?{type:!1}:y));const x=h.animation;Vo(l)&&(l.add(d),x.then(()=>l.remove(d))),u.push(x)}return o&&Promise.all(u).then(()=>{o&&cC(e,o)}),u}function ju(e,t,n={}){const r=_a(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const s=r?()=>Promise.all(sv(e,r,n)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:d}=i;return gC(e,t,u+l,c,d,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[l,u]=a==="beforeChildren"?[s,o]:[o,s];return l().then(()=>u())}else return Promise.all([s(),o(n.delay)])}function gC(e,t,n=0,r=0,i=1,s){const o=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(vC).forEach((u,c)=>{u.notify("AnimationStart",t),o.push(ju(u,t,{...s,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function vC(e,t){return e.sortNodePosition(t)}function yC(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(s=>ju(e,s,n));r=Promise.all(i)}else if(typeof t=="string")r=ju(e,t,n);else{const i=typeof t=="function"?_a(e,t,n.custom):t;r=Promise.all(sv(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const wC=[...Bc].reverse(),xC=Bc.length;function SC(e){return t=>Promise.all(t.map(({animation:n,options:r})=>yC(e,n,r)))}function _C(e){let t=SC(e);const n=CC();let r=!0;const i=(l,u)=>{const c=_a(e,u);if(c){const{transition:d,transitionEnd:h,...f}=c;l={...l,...f,...h}}return l};function s(l){t=l(e)}function o(l,u){const c=e.getProps(),d=e.getVariantContext(!0)||{},h=[],f=new Set;let y={},w=1/0;for(let v=0;v<xC;v++){const p=wC[v],m=n[p],_=c[p]!==void 0?c[p]:d[p],k=ts(_),C=p===u?m.isActive:null;C===!1&&(w=v);let E=_===d[p]&&_!==c[p]&&k;if(E&&r&&e.manuallyAnimateOnMount&&(E=!1),m.protectedKeys={...y},!m.isActive&&C===null||!_&&!m.prevProp||ya(_)||typeof _=="boolean")continue;let A=kC(m.prevProp,_)||p===u&&m.isActive&&!E&&k||v>w&&k,O=!1;const ue=Array.isArray(_)?_:[_];let pe=ue.reduce(i,{});C===!1&&(pe={});const{prevResolvedValues:Ce={}}=m,ce={...Ce,...pe},Ht=ne=>{A=!0,f.has(ne)&&(O=!0,f.delete(ne)),m.needsAnimating[ne]=!0};for(const ne in ce){const Je=pe[ne],P=Ce[ne];if(y.hasOwnProperty(ne))continue;let L=!1;No(Je)&&No(P)?L=!Pg(Je,P):L=Je!==P,L?Je!==void 0?Ht(ne):f.add(ne):Je!==void 0&&f.has(ne)?Ht(ne):m.protectedKeys[ne]=!0}m.prevProp=_,m.prevResolvedValues=pe,m.isActive&&(y={...y,...pe}),r&&e.blockInitialAnimation&&(A=!1),A&&(!E||O)&&h.push(...ue.map(ne=>({animation:ne,options:{type:p,...l}})))}if(f.size){const v={};f.forEach(p=>{const m=e.getBaseTarget(p);m!==void 0&&(v[p]=m)}),h.push({animation:v})}let x=!!h.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(h):Promise.resolve()}function a(l,u,c){var d;if(n[l].isActive===u)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(f=>{var y;return(y=f.animationState)===null||y===void 0?void 0:y.setActive(l,u)}),n[l].isActive=u;const h=o(c,l);for(const f in n)n[f].protectedKeys={};return h}return{animateChanges:o,setActive:a,setAnimateFunction:s,getState:()=>n}}function kC(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Pg(t,e):!1}function Rn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function CC(){return{animate:Rn(!0),whileInView:Rn(),whileHover:Rn(),whileTap:Rn(),whileDrag:Rn(),whileFocus:Rn(),exit:Rn()}}class EC extends bn{constructor(t){super(t),t.animationState||(t.animationState=_C(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),ya(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let bC=0;class TC extends bn{constructor(){super(...arguments),this.id=bC++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const s=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&s.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const PC={animation:{Feature:EC},exit:{Feature:TC}},of=(e,t)=>Math.abs(e-t);function jC(e,t){const n=of(e.x,t.x),r=of(e.y,t.y);return Math.sqrt(n**2+r**2)}class ov{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=dl(this.lastMoveEventInfo,this.history),h=this.startEvent!==null,f=jC(d.offset,{x:0,y:0})>=3;if(!h&&!f)return;const{point:y}=d,{timestamp:w}=Se;this.history.push({...y,timestamp:w});const{onStart:x,onMove:v}=this.handlers;h||(x&&x(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),v&&v(this.lastMoveEvent,d)},this.handlePointerMove=(d,h)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=cl(h,this.transformPagePoint),H.update(this.updatePoint,!0)},this.handlePointerUp=(d,h)=>{this.end();const{onEnd:f,onSessionEnd:y,resumeAnimation:w}=this.handlers;if(this.dragSnapToOrigin&&w&&w(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=dl(d.type==="pointercancel"?this.lastMoveEventInfo:cl(h,this.transformPagePoint),this.history);this.startEvent&&f&&f(d,x),y&&y(d,x)},!kg(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const o=Sa(t),a=cl(o,this.transformPagePoint),{point:l}=a,{timestamp:u}=Se;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,dl(a,this.history)),this.removeListeners=fn(Lt(this.contextWindow,"pointermove",this.handlePointerMove),Lt(this.contextWindow,"pointerup",this.handlePointerUp),Lt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Vt(this.updatePoint)}}function cl(e,t){return t?{point:t(e.point)}:e}function af(e,t){return{x:e.x-t.x,y:e.y-t.y}}function dl({point:e},t){return{point:e,delta:af(e,av(t)),offset:af(e,AC(t)),velocity:RC(t,.1)}}function AC(e){return e[0]}function av(e){return e[e.length-1]}function RC(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=av(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>pn(t)));)n--;if(!r)return{x:0,y:0};const s=It(i.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const o={x:(i.x-r.x)/s,y:(i.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Ke(e){return e.max-e.min}function Au(e,t=0,n=.01){return Math.abs(e-t)<=n}function lf(e,t,n,r=.5){e.origin=r,e.originPoint=Y(t.min,t.max,e.origin),e.scale=Ke(n)/Ke(t),(Au(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=Y(n.min,n.max,e.origin)-e.originPoint,(Au(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Ii(e,t,n,r){lf(e.x,t.x,n.x,r?r.originX:void 0),lf(e.y,t.y,n.y,r?r.originY:void 0)}function uf(e,t,n){e.min=n.min+t.min,e.max=e.min+Ke(t)}function OC(e,t,n){uf(e.x,t.x,n.x),uf(e.y,t.y,n.y)}function cf(e,t,n){e.min=t.min-n.min,e.max=e.min+Ke(t)}function Ni(e,t,n){cf(e.x,t.x,n.x),cf(e.y,t.y,n.y)}function LC(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?Y(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?Y(n,e,r.max):Math.min(e,n)),e}function df(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function IC(e,{top:t,left:n,bottom:r,right:i}){return{x:df(e.x,n,i),y:df(e.y,t,r)}}function hf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function NC(e,t){return{x:hf(e.x,t.x),y:hf(e.y,t.y)}}function MC(e,t){let n=.5;const r=Ke(e),i=Ke(t);return i>r?n=rs(t.min,t.max-r,e.min):r>i&&(n=rs(e.min,e.max-i,t.min)),yn(0,1,n)}function DC(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Ru=.35;function $C(e=Ru){return e===!1?e=0:e===!0&&(e=Ru),{x:ff(e,"left","right"),y:ff(e,"top","bottom")}}function ff(e,t,n){return{min:pf(e,t),max:pf(e,n)}}function pf(e,t){return typeof e=="number"?e:e[t]||0}const mf=()=>({translate:0,scale:1,origin:0,originPoint:0}),kr=()=>({x:mf(),y:mf()}),gf=()=>({min:0,max:0}),se=()=>({x:gf(),y:gf()});function Ye(e){return[e("x"),e("y")]}function lv({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function VC({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function zC(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function hl(e){return e===void 0||e===1}function Ou({scale:e,scaleX:t,scaleY:n}){return!hl(e)||!hl(t)||!hl(n)}function Mn(e){return Ou(e)||uv(e)||e.z||e.rotate||e.rotateX||e.rotateY}function uv(e){return vf(e.x)||vf(e.y)}function vf(e){return e&&e!=="0%"}function zo(e,t,n){const r=e-n,i=t*r;return n+i}function yf(e,t,n,r,i){return i!==void 0&&(e=zo(e,i,r)),zo(e,n,r)+t}function Lu(e,t=0,n=1,r,i){e.min=yf(e.min,t,n,r,i),e.max=yf(e.max,t,n,r,i)}function cv(e,{x:t,y:n}){Lu(e.x,t.translate,t.scale,t.originPoint),Lu(e.y,n.translate,n.scale,n.originPoint)}function BC(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let s,o;for(let a=0;a<i;a++){s=n[a],o=s.projectionDelta;const l=s.instance;l&&l.style&&l.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&Cr(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,cv(e,o)),r&&Mn(s.latestValues)&&Cr(e,s.latestValues))}t.x=wf(t.x),t.y=wf(t.y)}function wf(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Qt(e,t){e.min=e.min+t,e.max=e.max+t}function xf(e,t,[n,r,i]){const s=t[i]!==void 0?t[i]:.5,o=Y(e.min,e.max,s);Lu(e,t[n],t[r],o,t.scale)}const UC=["x","scaleX","originX"],FC=["y","scaleY","originY"];function Cr(e,t){xf(e.x,t,UC),xf(e.y,t,FC)}function dv(e,t){return lv(zC(e.getBoundingClientRect(),t))}function HC(e,t,n){const r=dv(e,n),{scroll:i}=t;return i&&(Qt(r.x,i.offset.x),Qt(r.y,i.offset.y)),r}const hv=({current:e})=>e?e.ownerDocument.defaultView:null,WC=new WeakMap;class KC{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=se(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Sa(c,"page").point)},s=(c,d)=>{const{drag:h,dragPropagation:f,onDragStart:y}=this.getProps();if(h&&!f&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Eg(h),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ye(x=>{let v=this.getAxisMotionValue(x).get()||0;if(St.test(v)){const{projection:p}=this.visualElement;if(p&&p.layout){const m=p.layout.layoutBox[x];m&&(v=Ke(m)*(parseFloat(v)/100))}}this.originPoint[x]=v}),y&&H.update(()=>y(c,d),!1,!0);const{animationState:w}=this.visualElement;w&&w.setActive("whileDrag",!0)},o=(c,d)=>{const{dragPropagation:h,dragDirectionLock:f,onDirectionLock:y,onDrag:w}=this.getProps();if(!h&&!this.openGlobalLock)return;const{offset:x}=d;if(f&&this.currentDirection===null){this.currentDirection=GC(x),this.currentDirection!==null&&y&&y(this.currentDirection);return}this.updateAxis("x",d.point,x),this.updateAxis("y",d.point,x),this.visualElement.render(),w&&w(c,d)},a=(c,d)=>this.stop(c,d),l=()=>Ye(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new ov(t,{onSessionStart:i,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:hv(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&H.update(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Vs(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=LC(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,s=this.constraints;n&&Sr(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=IC(i.layoutBox,n):this.constraints=!1,this.elastic=$C(r),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Ye(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=DC(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Sr(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=HC(r,i.root,this.visualElement.getTransformPagePoint());let o=NC(i.layout.layoutBox,s);if(n){const a=n(VC(o));this.hasMutatedConstraints=!!a,a&&(o=lv(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Ye(c=>{if(!Vs(c,n,this.currentDirection))return;let d=l&&l[c]||{};o&&(d={min:0,max:0});const h=i?200:1e6,f=i?40:1e7,y={type:"inertia",velocity:r?t[c]:0,bounceStiffness:h,bounceDamping:f,timeConstant:750,restDelta:1,restSpeed:10,...s,...d};return this.startAxisValueAnimation(c,y)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(rd(t,r,0,n))}stopAnimation(){Ye(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ye(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Ye(n=>{const{drag:r}=this.getProps();if(!Vs(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];s.set(t[n]-Y(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Sr(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Ye(o=>{const a=this.getAxisMotionValue(o);if(a){const l=a.get();i[o]=MC({min:l,max:l},this.constraints[o])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ye(o=>{if(!Vs(o,t,null))return;const a=this.getAxisMotionValue(o),{min:l,max:u}=this.constraints[o];a.set(Y(l,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;WC.set(this.visualElement,this);const t=this.visualElement.current,n=Lt(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Sr(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const o=Rt(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Ye(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=l[c].translate,d.set(d.get()+l[c].translate))}),this.visualElement.render())});return()=>{o(),n(),s(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Ru,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:a}}}function Vs(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function GC(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class qC extends bn{constructor(t){super(t),this.removeGroupControls=te,this.removeListeners=te,this.controls=new KC(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||te}unmount(){this.removeGroupControls(),this.removeListeners()}}const Sf=e=>(t,n)=>{e&&H.update(()=>e(t,n))};class JC extends bn{constructor(){super(...arguments),this.removePointerDownListener=te}onPointerDown(t){this.session=new ov(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:hv(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Sf(t),onStart:Sf(n),onMove:r,onEnd:(s,o)=>{delete this.session,i&&H.update(()=>i(s,o))}}}mount(){this.removePointerDownListener=Lt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function QC(){const e=S.useContext(ga);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=S.useId();return S.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const to={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function _f(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ci={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(R.test(e))e=parseFloat(e);else return e;const n=_f(e,t.target.x),r=_f(e,t.target.y);return`${n}% ${r}%`}},YC={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=wn.parse(e);if(i.length>5)return r;const s=wn.createTransformer(e),o=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;i[0+o]/=a,i[1+o]/=l;const u=Y(a,l,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),s(i)}};class XC extends Fe.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:s}=t;sk(ZC),s&&(n.group&&n.group.add(s),r&&r.register&&i&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),to.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:s}=this.props,o=r.projection;return o&&(o.isPresent=s,i||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||H.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function fv(e){const[t,n]=QC(),r=S.useContext(Fc);return Fe.createElement(XC,{...e,layoutGroup:r,switchLayoutGroup:S.useContext(lg),isPresent:t,safeToRemove:n})}const ZC={borderRadius:{...ci,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ci,borderTopRightRadius:ci,borderBottomLeftRadius:ci,borderBottomRightRadius:ci,boxShadow:YC},pv=["TopLeft","TopRight","BottomLeft","BottomRight"],eE=pv.length,kf=e=>typeof e=="string"?parseFloat(e):e,Cf=e=>typeof e=="number"||R.test(e);function tE(e,t,n,r,i,s){i?(e.opacity=Y(0,n.opacity!==void 0?n.opacity:1,nE(r)),e.opacityExit=Y(t.opacity!==void 0?t.opacity:1,0,rE(r))):s&&(e.opacity=Y(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<eE;o++){const a=`border${pv[o]}Radius`;let l=Ef(t,a),u=Ef(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||Cf(l)===Cf(u)?(e[a]=Math.max(Y(kf(l),kf(u),r),0),(St.test(u)||St.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=Y(t.rotate||0,n.rotate||0,r))}function Ef(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const nE=mv(0,.5,Dg),rE=mv(.5,.95,te);function mv(e,t,n){return r=>r<e?0:r>t?1:n(rs(e,t,r))}function bf(e,t){e.min=t.min,e.max=t.max}function Qe(e,t){bf(e.x,t.x),bf(e.y,t.y)}function Tf(e,t,n,r,i){return e-=t,e=zo(e,1/n,r),i!==void 0&&(e=zo(e,1/i,r)),e}function iE(e,t=0,n=1,r=.5,i,s=e,o=e){if(St.test(t)&&(t=parseFloat(t),t=Y(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=Y(s.min,s.max,r);e===s&&(a-=t),e.min=Tf(e.min,t,n,a,i),e.max=Tf(e.max,t,n,a,i)}function Pf(e,t,[n,r,i],s,o){iE(e,t[n],t[r],t[i],t.scale,s,o)}const sE=["x","scaleX","originX"],oE=["y","scaleY","originY"];function jf(e,t,n,r){Pf(e.x,t,sE,n?n.x:void 0,r?r.x:void 0),Pf(e.y,t,oE,n?n.y:void 0,r?r.y:void 0)}function Af(e){return e.translate===0&&e.scale===1}function gv(e){return Af(e.x)&&Af(e.y)}function aE(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function vv(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Rf(e){return Ke(e.x)/Ke(e.y)}class lE{constructor(){this.members=[]}add(t){id(this.members,t),t.scheduleRender()}remove(t){if(sd(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const s=this.members[i];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Of(e,t,n){let r="";const i=e.x.translate/t.x,s=e.y.translate/t.y;if((i||s)&&(r=`translate3d(${i}px, ${s}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:u,rotateY:c}=n;l&&(r+=`rotate(${l}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const o=e.x.scale*t.x,a=e.y.scale*t.y;return(o!==1||a!==1)&&(r+=`scale(${o}, ${a})`),r||"none"}const uE=(e,t)=>e.depth-t.depth;class cE{constructor(){this.children=[],this.isDirty=!1}add(t){id(this.children,t),this.isDirty=!0}remove(t){sd(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(uE),this.isDirty=!1,this.children.forEach(t)}}function dE(e,t){const n=performance.now(),r=({timestamp:i})=>{const s=i-n;s>=t&&(Vt(r),e(s-t))};return H.read(r,!0),()=>Vt(r)}function hE(e){window.MotionDebug&&window.MotionDebug.record(e)}function fE(e){return e instanceof SVGElement&&e.tagName!=="svg"}function pE(e,t,n){const r=$e(e)?e:Hr(e);return r.start(rd("",r,t,n)),r.animation}const Lf=["","X","Y","Z"],mE={visibility:"hidden"},If=1e3;let gE=0;const Dn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function yv({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(o={},a=t==null?void 0:t()){this.id=gE++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Dn.totalNodes=Dn.resolvedTargetDeltas=Dn.recalculatedProjection=0,this.nodes.forEach(wE),this.nodes.forEach(CE),this.nodes.forEach(EE),this.nodes.forEach(xE),hE(Dn)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new cE)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new od),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=fE(o),this.instance=o;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let d;const h=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=dE(h,250),to.hasAnimatedSinceResize&&(to.hasAnimatedSinceResize=!1,this.nodes.forEach(Mf))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:h,hasRelativeTargetChanged:f,layout:y})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const w=this.options.transition||c.getDefaultTransition()||AE,{onLayoutAnimationStart:x,onLayoutAnimationComplete:v}=c.getProps(),p=!this.targetLayout||!vv(this.targetLayout,y)||f,m=!h&&f;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||m||h&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,m);const _={...nd(w,"layout"),onPlay:x,onComplete:v};(c.shouldReduceMotion||this.options.layoutRoot)&&(_.delay=0,_.type=!1),this.startAnimation(_)}else h||Mf(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=y})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Vt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(bE),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Nf);return}this.isUpdating||this.nodes.forEach(_E),this.isUpdating=!1,this.nodes.forEach(kE),this.nodes.forEach(vE),this.nodes.forEach(yE),this.clearAllSnapshots();const a=performance.now();Se.delta=yn(0,1e3/60,a-Se.timestamp),Se.timestamp=a,Se.isProcessing=!0,nl.update.process(Se),nl.preRender.process(Se),nl.render.process(Se),Se.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(SE),this.sharedNodes.forEach(TE)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,H.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){H.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=se(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:o,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!gv(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;o&&(a||Mn(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),RE(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return se();const a=o.measureViewportBox(),{scroll:l}=this.root;return l&&(Qt(a.x,l.offset.x),Qt(a.y,l.offset.y)),a}removeElementScroll(o){const a=se();Qe(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:d}=u;if(u!==this.root&&c&&d.layoutScroll){if(c.isRoot){Qe(a,o);const{scroll:h}=this.root;h&&(Qt(a.x,-h.offset.x),Qt(a.y,-h.offset.y))}Qt(a.x,c.offset.x),Qt(a.y,c.offset.y)}}return a}applyTransform(o,a=!1){const l=se();Qe(l,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Cr(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Mn(c.latestValues)&&Cr(l,c.latestValues)}return Mn(this.latestValues)&&Cr(l,this.latestValues),l}removeTransform(o){const a=se();Qe(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Mn(u.latestValues))continue;Ou(u.latestValues)&&u.updateSnapshot();const c=se(),d=u.measurePageBox();Qe(c,d),jf(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return Mn(this.latestValues)&&jf(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Se.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:d,layoutId:h}=this.options;if(!(!this.layout||!(d||h))){if(this.resolvedRelativeTargetAt=Se.timestamp,!this.targetDelta&&!this.relativeTarget){const f=this.getClosestProjectingParent();f&&f.layout&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=se(),this.relativeTargetOrigin=se(),Ni(this.relativeTargetOrigin,this.layout.layoutBox,f.layout.layoutBox),Qe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=se(),this.targetWithTransforms=se()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),OC(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Qe(this.target,this.layout.layoutBox),cv(this.target,this.targetDelta)):Qe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const f=this.getClosestProjectingParent();f&&!!f.resumingFrom==!!this.resumingFrom&&!f.options.layoutScroll&&f.target&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=se(),this.relativeTargetOrigin=se(),Ni(this.relativeTargetOrigin,this.target,f.target),Qe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Dn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ou(this.parent.latestValues)||uv(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===Se.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;Qe(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;BC(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:y}=a;if(!y){this.projectionTransform&&(this.projectionDelta=kr(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=kr(),this.projectionDeltaWithTransform=kr());const w=this.projectionTransform;Ii(this.projectionDelta,this.layoutCorrected,y,this.latestValues),this.projectionTransform=Of(this.projectionDelta,this.treeScale),(this.projectionTransform!==w||this.treeScale.x!==h||this.treeScale.y!==f)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",y)),Dn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(o,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},d=kr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const h=se(),f=l?l.source:void 0,y=this.layout?this.layout.source:void 0,w=f!==y,x=this.getStack(),v=!x||x.members.length<=1,p=!!(w&&!v&&this.options.crossfade===!0&&!this.path.some(jE));this.animationProgress=0;let m;this.mixTargetDelta=_=>{const k=_/1e3;Df(d.x,o.x,k),Df(d.y,o.y,k),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Ni(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),PE(this.relativeTarget,this.relativeTargetOrigin,h,k),m&&aE(this.relativeTarget,m)&&(this.isProjectionDirty=!1),m||(m=se()),Qe(m,this.relativeTarget)),w&&(this.animationValues=c,tE(c,u,this.latestValues,k,p,v)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=k},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Vt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=H.update(()=>{to.hasAnimatedSinceResize=!0,this.currentAnimation=pE(0,If,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(If),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=o;if(!(!a||!l||!u)){if(this!==o&&this.layout&&u&&wv(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||se();const d=Ke(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+d;const h=Ke(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+h}Qe(a,l),Cr(a,c),Ii(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new lE),this.sharedNodes.get(o).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const u={};for(let c=0;c<Lf.length;c++){const d="rotate"+Lf[c];l[d]&&(u[d]=l[d],o.setStaticValue(d,0))}o.render();for(const c in u)o.setStaticValue(c,u[c]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return mE;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=eo(o==null?void 0:o.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const w={};return this.options.layoutId&&(w.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,w.pointerEvents=eo(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Mn(this.latestValues)&&(w.transform=c?c({},""):"none",this.hasProjected=!1),w}const h=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=Of(this.projectionDeltaWithTransform,this.treeScale,h),c&&(u.transform=c(h,u.transform));const{x:f,y}=this.projectionDelta;u.transformOrigin=`${f.origin*100}% ${y.origin*100}% 0`,d.animationValues?u.opacity=d===this?(l=(a=h.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:h.opacityExit:u.opacity=d===this?h.opacity!==void 0?h.opacity:"":h.opacityExit!==void 0?h.opacityExit:0;for(const w in Lo){if(h[w]===void 0)continue;const{correct:x,applyTo:v}=Lo[w],p=u.transform==="none"?h[w]:x(h[w],d);if(v){const m=v.length;for(let _=0;_<m;_++)u[v[_]]=p}else u[w]=p}return this.options.layoutId&&(u.pointerEvents=d===this?eo(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Nf),this.root.sharedNodes.clear()}}}function vE(e){e.updateLayout()}function yE(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:s}=e.options,o=n.source!==e.layout.source;s==="size"?Ye(d=>{const h=o?n.measuredBox[d]:n.layoutBox[d],f=Ke(h);h.min=r[d].min,h.max=h.min+f}):wv(s,n.layoutBox,r)&&Ye(d=>{const h=o?n.measuredBox[d]:n.layoutBox[d],f=Ke(r[d]);h.max=h.min+f,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+f)});const a=kr();Ii(a,r,n.layoutBox);const l=kr();o?Ii(l,e.applyTransform(i,!0),n.measuredBox):Ii(l,r,n.layoutBox);const u=!gv(a);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:h,layout:f}=d;if(h&&f){const y=se();Ni(y,n.layoutBox,h.layoutBox);const w=se();Ni(w,r,f.layoutBox),vv(y,w)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=w,e.relativeTargetOrigin=y,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function wE(e){Dn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function xE(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function SE(e){e.clearSnapshot()}function Nf(e){e.clearMeasurements()}function _E(e){e.isLayoutDirty=!1}function kE(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Mf(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function CE(e){e.resolveTargetDelta()}function EE(e){e.calcProjection()}function bE(e){e.resetRotation()}function TE(e){e.removeLeadSnapshot()}function Df(e,t,n){e.translate=Y(t.translate,0,n),e.scale=Y(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function $f(e,t,n,r){e.min=Y(t.min,n.min,r),e.max=Y(t.max,n.max,r)}function PE(e,t,n,r){$f(e.x,t.x,n.x,r),$f(e.y,t.y,n.y,r)}function jE(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const AE={duration:.45,ease:[.4,0,.1,1]},Vf=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),zf=Vf("applewebkit/")&&!Vf("chrome/")?Math.round:te;function Bf(e){e.min=zf(e.min),e.max=zf(e.max)}function RE(e){Bf(e.x),Bf(e.y)}function wv(e,t,n){return e==="position"||e==="preserve-aspect"&&!Au(Rf(t),Rf(n),.2)}const OE=yv({attachResizeListener:(e,t)=>Rt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),fl={current:void 0},xv=yv({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!fl.current){const e=new OE({});e.mount(window),e.setOptions({layoutScroll:!0}),fl.current=e}return fl.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),LE={pan:{Feature:JC},drag:{Feature:qC,ProjectionNode:xv,MeasureLayout:fv}},IE=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function NE(e){const t=IE.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Iu(e,t,n=1){const[r,i]=NE(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const o=s.trim();return nv(o)?parseFloat(o):o}else return ku(i)?Iu(i,t,n+1):i}function ME(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const s=i.get();if(!ku(s))return;const o=Iu(s,r);o&&i.set(o)});for(const i in t){const s=t[i];if(!ku(s))continue;const o=Iu(s,r);o&&(t[i]=o,n||(n={}),n[i]===void 0&&(n[i]=s))}return{target:t,transitionEnd:n}}const DE=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Sv=e=>DE.has(e),$E=e=>Object.keys(e).some(Sv),Uf=e=>e===nr||e===R,Ff=(e,t)=>parseFloat(e.split(", ")[t]),Hf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return Ff(i[1],t);{const s=r.match(/^matrix\((.+)\)$/);return s?Ff(s[1],e):0}},VE=new Set(["x","y","z"]),zE=ds.filter(e=>!VE.has(e));function BE(e){const t=[];return zE.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const Wr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Hf(4,13),y:Hf(5,14)};Wr.translateX=Wr.x;Wr.translateY=Wr.y;const UE=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,s=getComputedStyle(i),{display:o}=s,a={};o==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{a[u]=Wr[u](r,s)}),t.render();const l=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(a[u]),e[u]=Wr[u](l,s)}),e},FE=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(Sv);let s=[],o=!1;const a=[];if(i.forEach(l=>{const u=e.getValue(l);if(!e.hasValue(l))return;let c=n[l],d=ui(c);const h=t[l];let f;if(No(h)){const y=h.length,w=h[0]===null?1:0;c=h[w],d=ui(c);for(let x=w;x<y&&h[x]!==null;x++)f?Yc(ui(h[x])===f):f=ui(h[x])}else f=ui(h);if(d!==f)if(Uf(d)&&Uf(f)){const y=u.get();typeof y=="string"&&u.set(parseFloat(y)),typeof h=="string"?t[l]=parseFloat(h):Array.isArray(h)&&f===R&&(t[l]=h.map(parseFloat))}else d!=null&&d.transform&&(f!=null&&f.transform)&&(c===0||h===0)?c===0?u.set(f.transform(c)):t[l]=d.transform(h):(o||(s=BE(e),o=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],u.jump(h))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,u=UE(t,e,a);return s.length&&s.forEach(([c,d])=>{e.getValue(c).set(d)}),e.render(),va&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function HE(e,t,n,r){return $E(t)?FE(e,t,n,r):{target:t,transitionEnd:r}}const WE=(e,t,n,r)=>{const i=ME(e,t,r);return t=i.target,r=i.transitionEnd,HE(e,t,n,r)},Nu={current:null},_v={current:!1};function KE(){if(_v.current=!0,!!va)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Nu.current=e.matches;e.addListener(t),t()}else Nu.current=!1}function GE(e,t,n){const{willChange:r}=t;for(const i in t){const s=t[i],o=n[i];if($e(s))e.addValue(i,s),Vo(r)&&r.add(i);else if($e(o))e.addValue(i,Hr(s,{owner:e})),Vo(r)&&r.remove(i);else if(o!==s)if(e.hasValue(i)){const a=e.getValue(i);!a.hasAnimated&&a.set(s)}else{const a=e.getStaticValue(i);e.addValue(i,Hr(a!==void 0?a:s,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const Wf=new WeakMap,kv=Object.keys(ns),qE=kv.length,Kf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],JE=Uc.length;class QE{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>H.render(this.render,!1,!0);const{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=wa(n),this.isVariantNode=ag(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const d in c){const h=c[d];a[d]!==void 0&&$e(h)&&(h.set(a[d],!1),Vo(u)&&u.add(d))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,Wf.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),_v.current||KE(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Nu.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Wf.delete(this.current),this.projection&&this.projection.unmount(),Vt(this.notifyUpdate),Vt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=tr.has(t),i=n.on("change",o=>{this.latestValues[t]=o,this.props.onUpdate&&H.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),s()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,s){let o,a;for(let l=0;l<qE;l++){const u=kv[l],{isEnabled:c,Feature:d,ProjectionNode:h,MeasureLayout:f}=ns[u];h&&(o=h),c(n)&&(!this.features[u]&&d&&(this.features[u]=new d(this)),f&&(a=f))}if((this.type==="html"||this.type==="svg")&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:c,dragConstraints:d,layoutScroll:h,layoutRoot:f}=n;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!c||d&&Sr(d),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:s,layoutScroll:h,layoutRoot:f})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):se()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Kf.length;r++){const i=Kf[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=GE(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<JE;r++){const i=Uc[r],s=this.props[i];(ts(s)||s===!1)&&(n[i]=s)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Hr(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=Qc(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!$e(s)?s:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new od),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Cv extends QE{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},s){let o=fC(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),o&&(o=i(o))),s){dC(this,r,o);const a=WE(this,r,o,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function YE(e){return window.getComputedStyle(e)}class XE extends Cv{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(tr.has(n)){const r=td(n);return r&&r.default||0}else{const r=YE(t),i=(dg(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return dv(t,n)}build(t,n,r,i){Wc(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Jc(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;$e(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){vg(t,n,r,i)}}class ZE extends Cv{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(tr.has(n)){const r=td(n);return r&&r.default||0}return n=yg.has(n)?n:zc(n),t.getAttribute(n)}measureInstanceViewportBox(){return se()}scrapeMotionValuesFromProps(t,n){return xg(t,n)}build(t,n,r,i){Gc(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){wg(t,n,r,i)}mount(t){this.isSVGTag=qc(t.tagName),super.mount(t)}}const eb=(e,t)=>Hc(e)?new ZE(t,{enableHardwareAcceleration:!1}):new XE(t,{enableHardwareAcceleration:!0}),tb={layout:{ProjectionNode:xv,MeasureLayout:fv}},nb={...PC,...qk,...LE,...tb},J=rk((e,t)=>Nk(e,t,nb,eb));function Ev(){const e=S.useRef(!1);return Vc(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function rb(){const e=Ev(),[t,n]=S.useState(0),r=S.useCallback(()=>{e.current&&n(t+1)},[t]);return[S.useCallback(()=>H.postRender(r),[r]),t]}class ib extends S.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function sb({children:e,isPresent:t}){const n=S.useId(),r=S.useRef(null),i=S.useRef({width:0,height:0,top:0,left:0});return S.useInsertionEffect(()=>{const{width:s,height:o,top:a,left:l}=i.current;if(t||!r.current||!s||!o)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${s}px !important;
            height: ${o}px !important;
            top: ${a}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),S.createElement(ib,{isPresent:t,childRef:r,sizeRef:i},S.cloneElement(e,{ref:r}))}const pl=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:s,mode:o})=>{const a=Sg(ob),l=S.useId(),u=S.useMemo(()=>({id:l,initial:t,isPresent:n,custom:i,onExitComplete:c=>{a.set(c,!0);for(const d of a.values())if(!d)return;r&&r()},register:c=>(a.set(c,!1),()=>a.delete(c))}),s?void 0:[n]);return S.useMemo(()=>{a.forEach((c,d)=>a.set(d,!1))},[n]),S.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),o==="popLayout"&&(e=S.createElement(sb,{isPresent:n},e)),S.createElement(ga.Provider,{value:u},e)};function ob(){return new Map}function ab(e){return S.useEffect(()=>()=>e(),[])}const $n=e=>e.key||"";function lb(e,t){e.forEach(n=>{const r=$n(n);t.set(r,n)})}function ub(e){const t=[];return S.Children.forEach(e,n=>{S.isValidElement(n)&&t.push(n)}),t}const ad=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:i,presenceAffectsLayout:s=!0,mode:o="sync"})=>{const a=S.useContext(Fc).forceRender||rb()[0],l=Ev(),u=ub(e);let c=u;const d=S.useRef(new Map).current,h=S.useRef(c),f=S.useRef(new Map).current,y=S.useRef(!0);if(Vc(()=>{y.current=!1,lb(u,f),h.current=c}),ab(()=>{y.current=!0,f.clear(),d.clear()}),y.current)return S.createElement(S.Fragment,null,c.map(p=>S.createElement(pl,{key:$n(p),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:s,mode:o},p)));c=[...c];const w=h.current.map($n),x=u.map($n),v=w.length;for(let p=0;p<v;p++){const m=w[p];x.indexOf(m)===-1&&!d.has(m)&&d.set(m,void 0)}return o==="wait"&&d.size&&(c=[]),d.forEach((p,m)=>{if(x.indexOf(m)!==-1)return;const _=f.get(m);if(!_)return;const k=w.indexOf(m);let C=p;if(!C){const E=()=>{d.delete(m);const b=Array.from(f.keys()).filter(A=>!x.includes(A));if(b.forEach(A=>f.delete(A)),h.current=u.filter(A=>{const O=$n(A);return O===m||b.includes(O)}),!d.size){if(l.current===!1)return;a(),r&&r()}};C=S.createElement(pl,{key:$n(_),isPresent:!1,onExitComplete:E,custom:t,presenceAffectsLayout:s,mode:o},_),d.set(m,C)}c.splice(k,0,C)}),c=c.map(p=>{const m=p.key;return d.has(m)?p:S.createElement(pl,{key:$n(p),isPresent:!0,presenceAffectsLayout:s,mode:o},p)}),S.createElement(S.Fragment,null,d.size?c:c.map(p=>S.cloneElement(p)))},zt=({children:e,onClick:t,variant:n="primary",size:r="md",isLoading:i=!1,disabled:s=!1,type:o="button",className:a="","aria-label":l,"aria-describedby":u,"aria-expanded":c,"aria-controls":d,"aria-pressed":h,autoFocus:f=!1,...y})=>{const w="font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transform-gpu",x={primary:"bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500 hover:shadow-lg",secondary:"border-2 border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500 hover:shadow-md",danger:"bg-red-500 hover:bg-red-600 text-white focus:ring-red-500 hover:shadow-lg"},v={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"},p=`${w} ${x[n]} ${v[r]} ${a}`,m={hover:{scale:1.02,transition:{duration:.15,ease:"easeOut"}},tap:{scale:.98,transition:{duration:.1,ease:"easeInOut"}}};return g.jsx(J.button,{type:o,onClick:t,disabled:s||i,className:p,"aria-label":l,"aria-describedby":u,"aria-expanded":c,"aria-controls":d,"aria-pressed":h,"aria-busy":i,autoFocus:f,variants:m,whileHover:!s&&!i?"hover":void 0,whileTap:!s&&!i?"tap":void 0,...y,children:i?g.jsxs("div",{className:"flex items-center",children:[g.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[g.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),g.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):e})},Er=({label:e,placeholder:t,value:n,onChange:r,type:i="text",error:s,required:o=!1,disabled:a=!1,className:l="","aria-describedby":u,"aria-invalid":c,description:d,...h})=>{const f=S.useId(),y=`${f}-error`,w=`${f}-description`,x=`w-full px-3 py-2 border-2 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed ${s?"border-red-500":"border-gray-600 focus:border-primary-500"} ${l}`,v=[s?y:null,d?w:null,u].filter(Boolean).join(" ")||void 0;return g.jsxs("div",{className:"w-full",children:[e&&g.jsxs("label",{htmlFor:f,className:"block text-sm font-medium text-gray-300 mb-1",children:[e,o&&g.jsx("span",{className:"text-red-500 ml-1","aria-label":"required",children:"*"})]}),d&&g.jsx("p",{id:w,className:"text-sm text-gray-400 mb-1",children:d}),g.jsx("input",{id:f,type:i,value:n,onChange:p=>r(p.target.value),placeholder:t,disabled:a,required:o,"aria-invalid":c||!!s,"aria-describedby":v,className:x,...h}),s&&g.jsx("p",{id:y,className:"mt-1 text-sm text-red-500",role:"alert",children:s})]})};var bv={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Gf=Fe.createContext&&Fe.createContext(bv),mn=globalThis&&globalThis.__assign||function(){return mn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},mn.apply(this,arguments)},cb=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};function Tv(e){return e&&e.map(function(t,n){return Fe.createElement(t.tag,mn({key:n},t.attr),Tv(t.child))})}function I(e){return function(t){return Fe.createElement(db,mn({attr:mn({},e.attr)},t),Tv(e.child))}}function db(e){var t=function(n){var r=e.attr,i=e.size,s=e.title,o=cb(e,["attr","size","title"]),a=i||n.size||"1em",l;return n.className&&(l=n.className),e.className&&(l=(l?l+" ":"")+e.className),Fe.createElement("svg",mn({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,o,{className:l,style:mn(mn({color:e.color||n.color},n.style),e.style),height:a,width:a,xmlns:"http://www.w3.org/2000/svg"}),s&&Fe.createElement("title",null,s),e.children)};return Gf!==void 0?Fe.createElement(Gf.Consumer,null,function(n){return t(n)}):t(bv)}function hb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"}}]})(e)}function fT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"}}]})(e)}function pT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"}}]})(e)}function fb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}}]})(e)}function mT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"}},{tag:"path",attr:{d:"M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"}}]})(e)}function gT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"}}]})(e)}function pb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(e)}function vT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}function yT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(e)}function wT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"}},{tag:"path",attr:{d:"M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"}}]})(e)}function xT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"}}]})(e)}function mb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"}}]})(e)}function gb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"}}]})(e)}function vb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z",clipRule:"evenodd"}}]})(e)}function ST(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",clipRule:"evenodd"}}]})(e)}function _T(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M3 12v3c0 1.657 3.134 3 7 3s7-1.343 7-3v-3c0 1.657-3.134 3-7 3s-7-1.343-7-3z"}},{tag:"path",attr:{d:"M3 7v3c0 1.657 3.134 3 7 3s7-1.343 7-3V7c0 1.657-3.134 3-7 3S3 8.657 3 7z"}},{tag:"path",attr:{d:"M17 5c0 1.657-3.134 3-7 3S3 6.657 3 5s3.134-3 7-3 7 1.343 7 3z"}}]})(e)}function yb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z",clipRule:"evenodd"}}]})(e)}function kT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}function qf(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(e)}function CT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"}},{tag:"path",attr:{d:"M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"}}]})(e)}function ET(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}},{tag:"path",attr:{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"}}]})(e)}function bT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"}}]})(e)}function TT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z",clipRule:"evenodd"}}]})(e)}function PT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z",clipRule:"evenodd"}}]})(e)}function jT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"}}]})(e)}function AT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z",clipRule:"evenodd"}}]})(e)}function RT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",clipRule:"evenodd"}}]})(e)}function OT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z",clipRule:"evenodd"}}]})(e)}function LT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}},{tag:"path",attr:{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"}}]})(e)}function wb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(e)}function IT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(e)}function NT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"}}]})(e)}function xb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"}}]})(e)}function MT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"}}]})(e)}function Sb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"}}]})(e)}function DT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"}}]})(e)}function _b(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z",clipRule:"evenodd"}}]})(e)}function $T(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"}}]})(e)}function VT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z",clipRule:"evenodd"}}]})(e)}function zT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(e)}function BT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z",clipRule:"evenodd"}}]})(e)}function UT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",clipRule:"evenodd"}}]})(e)}function FT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(e)}function HT(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"}}]})(e)}function kb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.242 0 1 1 0 01-1.415-1.415 5 5 0 017.072 0 1 1 0 01-1.415 1.415zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(e)}function Cb(e){return I({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}const Eb=({id:e,label:t,checked:n,onChange:r,disabled:i=!1,error:s,description:o,size:a="md",className:l=""})=>{const u=f=>{r(f.target.checked)},d=(()=>{switch(a){case"sm":return{checkbox:"w-4 h-4",icon:"w-3 h-3",label:"text-sm",description:"text-xs"};case"lg":return{checkbox:"w-6 h-6",icon:"w-4 h-4",label:"text-lg",description:"text-sm"};case"md":default:return{checkbox:"w-5 h-5",icon:"w-3.5 h-3.5",label:"text-base",description:"text-sm"}}})(),h=e||`checkbox-${Math.random().toString(36).substr(2,9)}`;return g.jsxs("div",{className:`flex items-start space-x-3 ${l}`,children:[g.jsx("div",{className:"flex items-center h-5",children:g.jsxs("div",{className:"relative",children:[g.jsx("input",{id:h,type:"checkbox",checked:n,onChange:u,disabled:i,className:"sr-only","aria-describedby":o?`${h}-description`:void 0}),g.jsx("div",{className:`
              ${d.checkbox}
              border-2 rounded-md cursor-pointer transition-all duration-200
              ${n?"bg-primary-500 border-primary-500":"bg-background-secondary border-gray-600 hover:border-gray-500"}
              ${i?"opacity-50 cursor-not-allowed":"hover:shadow-sm"}
              ${s?"border-red-500":""}
              flex items-center justify-center
            `,onClick:()=>!i&&r(!n),children:n&&g.jsx(pb,{className:`${d.icon} text-white`,"aria-hidden":"true"})})]})}),(t||o)&&g.jsxs("div",{className:"flex-1 min-w-0",children:[t&&g.jsx("label",{htmlFor:h,className:`
                ${d.label}
                font-medium cursor-pointer
                ${i?"text-gray-500":"text-gray-300 hover:text-white"}
                ${s?"text-red-400":""}
                block
              `,children:t}),o&&g.jsx("p",{id:`${h}-description`,className:`
                ${d.description}
                ${i?"text-gray-600":"text-gray-400"}
                mt-1
              `,children:o}),s&&g.jsx("p",{className:"mt-1 text-sm text-red-500",children:s})]})]})},Pv=({onGoogleAuth:e,isLoading:t=!1,disabled:n=!1,text:r="Continue with Google"})=>g.jsxs(zt,{type:"button",onClick:e,isLoading:t,disabled:n,className:"w-full flex items-center justify-center gap-2 bg-background-secondary hover:bg-background-tertiary text-text-primary border border-border-primary font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background-primary disabled:opacity-50 disabled:cursor-not-allowed transform-gpu hover:shadow-md focus:ring-primary-500",children:[!t&&g.jsxs("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[g.jsx("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),g.jsx("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),g.jsx("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),g.jsx("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),r]}),bb=()=>{const[e,t]=S.useState(""),[n,r]=S.useState(""),[i,s]=S.useState(!0),[o,a]=S.useState(!1),[l,u]=S.useState({}),{login:c,signInWithGoogle:d,isLoading:h,isAuthenticated:f}=Zr(),y=En();S.useEffect(()=>{f&&y("/dashboard")},[f,y]);const w=()=>{const p={};return e?/\S+@\S+\.\S+/.test(e)||(p.email="Email is invalid"):p.email="Email is required",n||(p.password="Password is required"),u(p),Object.keys(p).length===0},x=async p=>{if(p.preventDefault(),!w())return;const m=await c(e,n,i);m.success?y("/dashboard"):u({general:m.error})},v=async()=>{a(!0),u({});try{const p=await d();p.success||(u({general:p.error||"Failed to sign in with Google"}),a(!1))}catch{u({general:"Failed to sign in with Google"}),a(!1)}};return g.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:g.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[g.jsxs("div",{children:[g.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Sign in to ChewyAI"}),g.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",g.jsx(vm,{to:"/signup",className:"font-medium text-primary-500 hover:text-primary-400",children:"create a new account"})]})]}),g.jsxs("div",{className:"space-y-4",children:[g.jsx(Pv,{onGoogleAuth:v,isLoading:o,disabled:h,text:"Sign in with Google"}),g.jsxs("div",{className:"relative",children:[g.jsx("div",{className:"absolute inset-0 flex items-center",children:g.jsx("div",{className:"w-full border-t border-gray-600"})}),g.jsx("div",{className:"relative flex justify-center text-sm",children:g.jsx("span",{className:"px-2 bg-background-primary text-gray-400",children:"Or continue with email"})})]})]}),g.jsxs("form",{className:"mt-8 space-y-6",onSubmit:x,children:[l.general&&g.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:l.general}),g.jsxs("div",{className:"space-y-4",children:[g.jsx(Er,{label:"Email address",type:"email",value:e,onChange:t,error:l.email,placeholder:"Enter your email",required:!0}),g.jsx(Er,{label:"Password",type:"password",value:n,onChange:r,error:l.password,placeholder:"Enter your password",required:!0})]}),g.jsx("div",{className:"flex items-center justify-between",children:g.jsx(Eb,{checked:i,onChange:s,label:"Stay signed in",description:"Keep me logged in across browser sessions",size:"sm"})}),g.jsx(zt,{type:"submit",isLoading:h,disabled:o,className:"w-full",size:"lg",children:"Sign in"})]})]})})},Tb=()=>{const[e,t]=S.useState({name:"",email:"",password:"",confirmPassword:""}),[n,r]=S.useState(!1),[i,s]=S.useState({}),{signup:o,signInWithGoogle:a,isLoading:l}=Zr(),u=En(),c=()=>{const y={};return e.email?/\S+@\S+\.\S+/.test(e.email)||(y.email="Email is invalid"):y.email="Email is required",e.password?e.password.length<6&&(y.password="Password must be at least 6 characters"):y.password="Password is required",e.password!==e.confirmPassword&&(y.confirmPassword="Passwords do not match"),s(y),Object.keys(y).length===0},d=async y=>{if(y.preventDefault(),!c())return;const w=await o(e.email,e.password,e.name||void 0);w.success?u("/dashboard"):s({general:w.error||"Signup failed"})},h=async()=>{r(!0),s({});try{const y=await a();y.success||(s({general:y.error||"Failed to sign up with Google"}),r(!1))}catch{s({general:"Failed to sign up with Google"}),r(!1)}},f=(y,w)=>t(x=>({...x,[y]:w}));return g.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:g.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[g.jsxs("div",{children:[g.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Create your account"}),g.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",g.jsx(vm,{to:"/login",className:"font-medium text-primary-500 hover:text-primary-400",children:"sign in to your existing account"})]})]}),g.jsxs("div",{className:"space-y-4",children:[g.jsx(Pv,{onGoogleAuth:h,isLoading:n,disabled:l,text:"Sign up with Google"}),g.jsxs("div",{className:"relative",children:[g.jsx("div",{className:"absolute inset-0 flex items-center",children:g.jsx("div",{className:"w-full border-t border-gray-600"})}),g.jsx("div",{className:"relative flex justify-center text-sm",children:g.jsx("span",{className:"px-2 bg-background-primary text-gray-400",children:"Or continue with email"})})]})]}),g.jsxs("form",{className:"mt-8 space-y-6",onSubmit:d,children:[i.general&&g.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:i.general}),g.jsxs("div",{className:"space-y-4",children:[g.jsx(Er,{label:"Full Name (Optional)",value:e.name,onChange:y=>f("name",y),placeholder:"Enter your full name"}),g.jsx(Er,{label:"Email address",type:"email",value:e.email,onChange:y=>f("email",y),error:i.email,placeholder:"Enter your email",required:!0}),g.jsx(Er,{label:"Password",type:"password",value:e.password,onChange:y=>f("password",y),error:i.password,placeholder:"Create a password",required:!0}),g.jsx(Er,{label:"Confirm Password",type:"password",value:e.confirmPassword,onChange:y=>f("confirmPassword",y),error:i.confirmPassword,placeholder:"Confirm your password",required:!0})]}),g.jsx(zt,{type:"submit",isLoading:l,disabled:n,className:"w-full",size:"lg",children:"Create Account"})]})]})})},Pb=()=>{const[e,t]=S.useState(null),{handleOAuthCallback:n}=Zr(),r=En();return S.useEffect(()=>{(async()=>{try{const s=await n();s.success?r("/dashboard",{replace:!0}):(t(s.error||"Authentication failed"),setTimeout(()=>{r("/login",{replace:!0})},3e3))}catch{t("Failed to process authentication"),setTimeout(()=>{r("/login",{replace:!0})},3e3)}})()},[n,r]),e?g.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:g.jsxs("div",{className:"max-w-md w-full text-center space-y-4 p-8",children:[g.jsxs("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:[g.jsx("h3",{className:"text-lg font-semibold",children:"Authentication Error"}),g.jsx("p",{className:"mt-2",children:e})]}),g.jsx("p",{className:"text-gray-400",children:"Redirecting to login page..."})]})}):g.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:g.jsxs("div",{className:"max-w-md w-full text-center space-y-4 p-8",children:[g.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"}),g.jsx("h3",{className:"text-lg font-semibold text-white",children:"Completing Authentication..."}),g.jsx("p",{className:"text-gray-400",children:"Please wait while we sign you in."})]})})},kt=({children:e})=>{const{isAuthenticated:t,isLoading:n,checkAuth:r}=Zr(),i=Cn();return S.useEffect(()=>{!t&&!n&&r()},[t,n,r]),n?g.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:g.jsxs("div",{className:"text-center",children:[g.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"}),g.jsx("p",{className:"text-gray-400",children:"Checking authentication..."})]})}):t?g.jsx(g.Fragment,{children:e}):g.jsx(uu,{to:"/login",state:{from:i},replace:!0})},jb=({credits:e,showDetails:t=!1,size:n="md",variant:r="inline"})=>{const i=En(),s=()=>{i("/credits")},o=()=>{const d="flex items-center justify-between transition-all duration-200";switch(r){case"sidebar":return`${d} bg-background-tertiary rounded-lg p-3 border border-gray-700 hover:border-primary-500/50`;case"card":return`${d} bg-background-secondary rounded-lg p-4 border border-gray-600`;case"inline":default:return`${d} bg-gray-800/50 rounded-lg p-2`}},l=(()=>{switch(n){case"sm":return{primary:"text-sm",secondary:"text-xs",icon:"w-4 h-4"};case"lg":return{primary:"text-lg",secondary:"text-sm",icon:"w-6 h-6"};case"md":default:return{primary:"text-sm",secondary:"text-xs",icon:"w-5 h-5"}}})(),u=e<5,c=e===0;return g.jsxs("div",{className:o(),children:[g.jsxs("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[g.jsx("div",{className:`
          flex items-center justify-center rounded-full p-1.5
          ${c?"bg-red-500/20 text-red-400":u?"bg-yellow-500/20 text-yellow-400":"bg-primary-500/20 text-primary-400"}
        `,children:g.jsx(_b,{className:l.icon})}),g.jsxs("div",{className:"flex-1 min-w-0",children:[g.jsxs("div",{className:"flex items-center space-x-1",children:[g.jsx("span",{className:`${l.primary} font-medium text-white`,children:e}),g.jsxs("span",{className:`${l.secondary} text-gray-400`,children:["credit",e!==1?"s":""]})]}),r==="sidebar"&&g.jsx("div",{className:`${l.secondary} text-gray-500 truncate`,children:c?"No credits remaining":u?"Running low":"Available for AI generation"})]})]}),(u||t)&&g.jsx("button",{onClick:s,className:`
            flex items-center justify-center rounded-lg transition-colors
            ${r==="sidebar"?"p-2 hover:bg-primary-500/20 text-primary-400 hover:text-primary-300":"p-1.5 hover:bg-gray-700 text-gray-400 hover:text-white"}
          `,title:"Buy more credits","aria-label":"Buy more credits",children:g.jsx(xb,{className:l.icon})})]})},Ab=({showWhenOnline:e=!1,position:t="top",className:n=""})=>{const{isOnline:r,isBackendAvailable:i,lastChecked:s,retryCount:o,isChecking:a,checkConnection:l}=Kn(),[u,c]=S.useState(!1),[d,h]=S.useState(null),f=r&&i,y=!r||!i;S.useEffect(()=>{if(y)c(!0),d&&(clearTimeout(d),h(null));else if(f)if(e){c(!0);const _=setTimeout(()=>{c(!1)},3e3);h(_)}else c(!1);return()=>{d&&clearTimeout(d)}},[r,i,e]);const w=async()=>{await l()},v=(()=>r?i?{icon:kb,message:"Connected",bgColor:"bg-green-500",textColor:"text-white",showRetry:!1}:{icon:qf,message:o>3?"Server temporarily unavailable":"Connecting to server...",bgColor:"bg-yellow-500",textColor:"text-white",showRetry:o>2}:{icon:qf,message:"No internet connection",bgColor:"bg-red-500",textColor:"text-white",showRetry:!1})(),p=v.icon;if(!u)return null;const m=t==="top"?"top-4 left-1/2 transform -translate-x-1/2":"bottom-4 left-1/2 transform -translate-x-1/2";return g.jsx(ad,{children:g.jsxs(J.div,{initial:{opacity:0,y:t==="top"?-50:50},animate:{opacity:1,y:0},exit:{opacity:0,y:t==="top"?-50:50},className:`
          fixed ${m} z-50 
          ${v.bgColor} ${v.textColor}
          px-4 py-2 rounded-lg shadow-lg border border-gray-600
          flex items-center space-x-2 text-sm font-medium
          ${n}
        `,children:[g.jsx(p,{className:`w-4 h-4 ${a?"animate-spin":""}`}),g.jsx("span",{children:v.message}),v.showRetry&&g.jsx("button",{onClick:w,disabled:a,className:"ml-2 p-1 rounded hover:bg-black/20 transition-colors disabled:opacity-50","aria-label":"Retry connection",children:g.jsx(Sb,{className:`w-3 h-3 ${a?"animate-spin":""}`})}),s&&y&&g.jsxs("span",{className:"text-xs opacity-75 ml-2",children:["Last checked: ",s.toLocaleTimeString()]})]})})},Rb=({className:e=""})=>{const{isOnline:t,isBackendAvailable:n,isChecking:r}=Kn(),i=t&&n,s=()=>t?n?"bg-green-500":"bg-yellow-500":"bg-red-500",o=()=>t?n?"Connected":"Server unavailable":"No internet connection";return g.jsxs("div",{className:`flex items-center space-x-2 ${e}`,title:o(),children:[g.jsx("div",{className:`
          w-2 h-2 rounded-full ${s()}
          ${r?"animate-pulse":""}
        `}),g.jsx("span",{className:"text-xs text-gray-400",children:i?"Online":"Offline"})]})},Ob=[{id:"study-sets",label:"Study Sets",path:"/dashboard",icon:gb,description:"Manage and study your flashcard sets"},{id:"documents",label:"Documents",path:"/documents",icon:yb,description:"Manage your documents"},{id:"analytics",label:"Analytics",path:"/analytics",icon:fb,description:"Study progress and insights"},{id:"credits",label:"Credits",path:"/credits",icon:vb,description:"Manage your AI credits"},{id:"help",label:"Help",path:"/help",icon:hb,description:"Get help and support"},{id:"settings",label:"Settings",path:"/settings",icon:mb,description:"Account and app settings"}],Lb=({isOpen:e,onToggle:t})=>{var h,f,y,w,x;const n=En(),r=Cn(),[i,s]=S.useState(!1),{user:o}=Zr();S.useEffect(()=>{const v=()=>{s(window.innerWidth<768)};return v(),window.addEventListener("resize",v),()=>window.removeEventListener("resize",v)},[]);const a=v=>v==="/dashboard"?r.pathname==="/dashboard"||r.pathname==="/":r.pathname.startsWith(v),l=v=>{n(v),i&&t()},u={open:{x:0,transition:{type:"spring",stiffness:300,damping:30}},closed:{x:"-100%",transition:{type:"spring",stiffness:300,damping:30}}},c={open:{opacity:1,transition:{duration:.2}},closed:{opacity:0,transition:{duration:.2}}},d={open:{opacity:1,x:0,transition:{type:"spring",stiffness:300,damping:30}},closed:{opacity:0,x:-20,transition:{duration:.2}}};return g.jsxs(g.Fragment,{children:[g.jsx(ad,{children:i&&e&&g.jsx(J.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden",variants:c,initial:"closed",animate:"open",exit:"closed",onClick:t})}),g.jsx(J.aside,{className:`
          fixed top-0 left-0 h-full bg-background-secondary border-r border-border-primary z-50
          ${i?"w-80":"w-64"}
          md:relative md:translate-x-0
        `,variants:u,initial:i?"closed":"open",animate:e?"open":"closed","aria-label":"Main navigation",children:g.jsxs("div",{className:"flex flex-col h-full",children:[g.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-border-primary",children:[g.jsxs("div",{className:"flex items-center space-x-3",children:[g.jsx("div",{className:"w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center",children:g.jsx("span",{className:"text-white font-bold text-lg",children:"C"})}),g.jsx("h1",{className:"text-xl font-bold text-white",children:"ChewyAI"})]}),i&&g.jsx("button",{onClick:t,className:"p-2 rounded-lg hover:bg-background-tertiary transition-colors","aria-label":"Close navigation",children:g.jsx(Cb,{className:"w-5 h-5 text-gray-400"})})]}),g.jsx("nav",{className:"flex-1 px-4 py-6 overflow-y-auto",children:g.jsx("ul",{className:"space-y-2",role:"list",children:Ob.map((v,p)=>{const m=v.icon,_=a(v.path);return g.jsx(J.li,{variants:d,initial:"closed",animate:e?"open":"closed",transition:{delay:p*.05},children:g.jsxs("button",{onClick:()=>l(v.path),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200 group
                        ${_?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,"aria-current":_?"page":void 0,"aria-describedby":`${v.id}-description`,children:[g.jsx(m,{className:`
                          w-5 h-5 transition-colors
                          ${_?"text-primary-400":"text-gray-400 group-hover:text-white"}
                        `}),g.jsxs("div",{className:"flex-1 min-w-0",children:[g.jsx("span",{className:"font-medium truncate block",children:v.label}),g.jsx("span",{id:`${v.id}-description`,className:"text-xs text-gray-500 group-hover:text-gray-400 truncate block",children:v.description})]}),_&&g.jsx(J.div,{className:"w-2 h-2 bg-primary-400 rounded-full",layoutId:"activeIndicator",transition:{type:"spring",stiffness:300,damping:30}})]})},v.id)})})}),o&&g.jsx("div",{className:"px-4 py-3 border-t border-border-primary",children:g.jsxs("div",{className:"flex items-center space-x-3",children:[g.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm",children:((f=(h=o.name)==null?void 0:h.charAt(0))==null?void 0:f.toUpperCase())||((w=(y=o.email)==null?void 0:y.charAt(0))==null?void 0:w.toUpperCase())||"U"}),g.jsxs("div",{className:"flex-1 min-w-0",children:[g.jsx("p",{className:"text-sm font-medium text-white truncate",children:o.name||((x=o.email)==null?void 0:x.split("@")[0])||"User"}),g.jsx("p",{className:"text-xs text-gray-400 truncate",children:o.subscription_tier||"Free"})]})]})}),g.jsxs("div",{className:"p-4 border-t border-border-primary space-y-3",children:[o&&g.jsx(jb,{credits:o.credits_remaining||0,variant:"sidebar",size:"sm",showDetails:!0}),g.jsx(Rb,{className:"justify-center"}),g.jsxs("div",{className:"text-xs text-gray-500 text-center",children:[g.jsx("p",{children:"ChewyAI v1.0.0"}),g.jsx("p",{className:"mt-1",children:"AI-Powered Study Materials"})]})]})]})}),i&&!e&&g.jsx("button",{onClick:t,className:"fixed top-4 left-4 z-40 p-3 bg-background-secondary border border-border-primary rounded-lg shadow-lg md:hidden","aria-label":"Open navigation menu",children:g.jsx(wb,{className:"w-6 h-6 text-white"})})]})},Ct=({children:e})=>{const[t,n]=S.useState(!1),[r,i]=S.useState(!1);S.useEffect(()=>{const o=()=>{const a=window.innerWidth<768;i(a),n(!a)};return o(),window.addEventListener("resize",o),()=>window.removeEventListener("resize",o)},[]);const s=()=>{n(!t)};return g.jsxs("div",{className:"min-h-screen bg-background-primary flex",children:[g.jsx(Lb,{isOpen:t,onToggle:s}),g.jsx("main",{className:`
          flex-1 transition-all duration-300 ease-in-out
          ml-0
          ${r?"w-full":""}
        `,id:"main-content",children:g.jsx("div",{className:"w-full h-full",children:e})})]})},Ib=({links:e})=>g.jsx("nav",{className:"sr-only focus-within:not-sr-only","aria-label":"Skip navigation",children:g.jsx("div",{className:"fixed top-0 left-0 z-50 bg-primary-600 text-white p-4 rounded-br-lg shadow-lg",children:g.jsx("ul",{className:"flex flex-col space-y-2",children:e.map((t,n)=>g.jsx("li",{children:g.jsx("a",{href:t.href,className:"block px-3 py-2 text-sm font-medium rounded-md hover:bg-primary-700 focus:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600",onClick:r=>{r.preventDefault();const i=document.querySelector(t.href);i&&(i.scrollIntoView({behavior:"smooth"}),i instanceof HTMLElement&&i.focus())},children:t.label})},n))})})}),Nb=[{keys:["?"],description:"Show keyboard shortcuts",context:"Global"},{keys:["Escape"],description:"Close modal or cancel action",context:"Global"},{keys:["←","→"],description:"Navigate between cards/questions",context:"Study"},{keys:["Space","↑","↓"],description:"Flip flashcard or select answer",context:"Study"},{keys:["F"],description:"Flag current item for review",context:"Study"},{keys:["Enter"],description:"Submit answer or continue",context:"Study"},{keys:["Ctrl","Z"],description:"Undo last action",context:"Study"},{keys:["Ctrl","Y"],description:"Redo last action",context:"Study"},{keys:["1","2","3","4"],description:"Select quiz answer option",context:"Quiz"},{keys:["G","D"],description:"Go to Dashboard",context:"Navigation"},{keys:["G","O"],description:"Go to Documents",context:"Navigation"},{keys:["G","S"],description:"Go to Study Sets",context:"Navigation"},{keys:["G","A"],description:"Go to Analytics",context:"Navigation"},{keys:["Ctrl","U"],description:"Upload new document",context:"Documents"},{keys:["Ctrl","F"],description:"Search documents",context:"Documents"},{keys:["Delete"],description:"Delete selected document",context:"Documents"}],jv=S.memo(({keys:e})=>g.jsx("div",{className:"flex items-center space-x-1",children:e.map((t,n)=>g.jsxs(Fe.Fragment,{children:[n>0&&g.jsx("span",{className:"text-gray-400 text-xs",children:"+"}),g.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-200 border border-gray-300 rounded-lg",children:t})]},t))}));jv.displayName="KeyboardKey";const Av=S.memo(({isOpen:e,onClose:t})=>{if(S.useEffect(()=>{const r=i=>{i.key==="Escape"&&t()};return e&&(document.addEventListener("keydown",r),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",r),document.body.style.overflow="unset"}},[e,t]),!e)return null;const n=Nb.reduce((r,i)=>{const s=i.context||"General";return r[s]||(r[s]=[]),r[s].push(i),r},{});return g.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:t,role:"dialog","aria-modal":"true","aria-labelledby":"shortcuts-title",children:g.jsxs("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",onClick:r=>r.stopPropagation(),children:[g.jsxs("div",{className:"flex items-center justify-between mb-6",children:[g.jsx("h2",{id:"shortcuts-title",className:"text-xl font-semibold text-white",children:"Keyboard Shortcuts"}),g.jsx(zt,{onClick:t,variant:"secondary",size:"sm","aria-label":"Close shortcuts modal",children:"✕"})]}),g.jsx("div",{className:"space-y-6",children:Object.entries(n).map(([r,i])=>g.jsxs("div",{children:[g.jsx("h3",{className:"text-lg font-medium text-gray-300 mb-3 border-b border-gray-600 pb-1",children:r}),g.jsx("div",{className:"space-y-2",children:i.map((s,o)=>g.jsxs("div",{className:"flex items-center justify-between py-2",children:[g.jsx("span",{className:"text-gray-400 flex-1",children:s.description}),g.jsx(jv,{keys:s.keys})]},o))})]},r))}),g.jsx("div",{className:"mt-6 pt-4 border-t border-gray-600",children:g.jsxs("p",{className:"text-sm text-gray-500 text-center",children:["Press ",g.jsx("kbd",{className:"px-1 py-0.5 text-xs bg-gray-200 text-gray-800 rounded",children:"?"})," anytime to show this help"]})})]})})});Av.displayName="KeyboardShortcutsModal";const Mb=(e={})=>{const{enableGlobalShortcuts:t=!0,enableNavigationShortcuts:n=!0}=e,r=En(),[i,s]=S.useState(!1),[o,a]=S.useState([]);return S.useEffect(()=>{if(!t)return;const l=u=>{var c;if(!(u.target instanceof HTMLInputElement||u.target instanceof HTMLTextAreaElement||u.target instanceof HTMLSelectElement||((c=u.target)==null?void 0:c.contentEditable)==="true")){if(u.key==="?"&&!u.ctrlKey&&!u.metaKey&&!u.altKey){u.preventDefault(),s(!0);return}if(u.key==="Escape"){s(!1),a([]);return}if(n){if(u.key.toLowerCase()==="g"&&!u.ctrlKey&&!u.metaKey&&!u.altKey){u.preventDefault(),a(["g"]),setTimeout(()=>a([]),2e3);return}if(o.includes("g")){switch(u.preventDefault(),u.key.toLowerCase()){case"d":case"s":r("/dashboard");break;case"o":r("/documents");break;case"a":r("/analytics");break}a([]);return}}if(u.ctrlKey||u.metaKey)switch(u.key.toLowerCase()){case"u":if(window.location.pathname==="/documents"){u.preventDefault();const d=new CustomEvent("trigger-upload");document.dispatchEvent(d)}break;case"f":if(window.location.pathname==="/documents"){u.preventDefault();const d=document.querySelector('input[placeholder*="search" i]');d&&d.focus()}break}}};return document.addEventListener("keydown",l),()=>{document.removeEventListener("keydown",l)}},[t,n,r,o]),{showShortcutsModal:i,setShowShortcutsModal:s,lastKeySequence:o}},ld=({isOpen:e,onClose:t,children:n,title:r,className:i="",closeOnOverlayClick:s=!0,closeOnEscape:o=!0,size:a="md"})=>{const l=S.useRef(null),u=S.useRef(null),c={sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg"};S.useEffect(()=>(e?(u.current=document.activeElement,document.body.style.overflow="hidden",setTimeout(()=>{var f;(f=l.current)==null||f.focus()},100)):(document.body.style.overflow="unset",u.current&&u.current.focus()),()=>{document.body.style.overflow="unset"}),[e]),S.useEffect(()=>{const f=y=>{var w;if(y.key==="Escape"&&o&&t(),y.key==="Tab"&&e){const x=(w=l.current)==null?void 0:w.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(x&&x.length>0){const v=x[0],p=x[x.length-1];y.shiftKey?document.activeElement===v&&(y.preventDefault(),p.focus()):document.activeElement===p&&(y.preventDefault(),v.focus())}}};return e&&document.addEventListener("keydown",f),()=>{document.removeEventListener("keydown",f)}},[e,o,t]);const d={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.2,ease:"easeOut"}},exit:{opacity:0,transition:{duration:.15,ease:"easeIn"}}},h={hidden:{opacity:0,scale:.9,y:-30,rotateX:15},visible:{opacity:1,scale:1,y:0,rotateX:0,transition:{type:"spring",damping:20,stiffness:300,mass:.8,opacity:{duration:.2}}},exit:{opacity:0,scale:.95,y:-10,transition:{duration:.15,ease:"easeIn"}}};return g.jsx(ad,{children:e&&g.jsxs(J.div,{className:"fixed inset-0 z-50 flex items-center justify-center p-4",initial:"hidden",animate:"visible",exit:"exit",children:[g.jsx(J.div,{className:"absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm",onClick:s?t:void 0,variants:d,initial:"hidden",animate:"visible",exit:"exit"}),g.jsxs(J.div,{ref:l,className:`
              relative w-full ${c[a]} bg-background-secondary 
              border border-gray-600 rounded-lg shadow-xl
              ${i}
            `,variants:h,initial:"hidden",animate:"visible",exit:"exit",role:"dialog","aria-modal":"true","aria-labelledby":r?"dialog-title":void 0,"aria-describedby":"dialog-content",tabIndex:-1,children:[r&&g.jsx("div",{className:"px-6 py-4 border-b border-gray-600",children:g.jsx("h2",{id:"dialog-title",className:"text-lg font-semibold text-white",children:r})}),g.jsx("div",{id:"dialog-content",className:"px-6 py-4",children:n})]})]})})},Db=({message:e,priority:t="polite",clearAfter:n=3e3})=>{const[r,i]=S.useState("");return S.useEffect(()=>{if(e&&(i(e),n>0)){const s=setTimeout(()=>{i("")},n);return()=>clearTimeout(s)}},[e,n]),g.jsx("div",{"aria-live":t,"aria-atomic":"true",className:"sr-only",role:"status",children:r})},$b=({isOpen:e,onClose:t,title:n,message:r,confirmText:i="OK",variant:s="info"})=>{const o=()=>{switch(s){case"success":return g.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-green-100 rounded-full flex items-center justify-center",children:g.jsx("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:g.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})});case"warning":return g.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-yellow-100 rounded-full flex items-center justify-center",children:g.jsx("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:g.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})});case"error":return g.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center",children:g.jsx("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:g.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})});default:return g.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-blue-100 rounded-full flex items-center justify-center",children:g.jsx("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:g.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}},a=()=>{switch(s){case"error":return"danger";default:return"primary"}},l={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.1}}},u={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3,ease:"easeOut"}}},c=()=>`${n?`${n}: `:""}${r}`;return g.jsxs(g.Fragment,{children:[g.jsx(Db,{message:e?c():"",priority:s==="error"?"assertive":"polite"}),g.jsx(ld,{isOpen:e,onClose:t,title:n,size:"sm",closeOnOverlayClick:!1,children:g.jsxs(J.div,{className:"text-center",variants:l,initial:"hidden",animate:"visible",children:[g.jsx(J.div,{variants:u,children:o()}),g.jsx(J.div,{className:"mt-4",variants:u,children:g.jsx("p",{className:"text-white leading-relaxed whitespace-pre-line",children:r})}),g.jsx(J.div,{className:"mt-6",variants:u,children:g.jsx(zt,{onClick:t,variant:a(),className:"w-full",autoFocus:!0,children:i})})]})})]})},Vb=({isOpen:e,onClose:t,onConfirm:n,title:r,message:i,confirmText:s="Confirm",cancelText:o="Cancel",variant:a="info",isLoading:l=!1,buttonLayout:u="default",additionalContent:c})=>{const d=()=>{n()},h=()=>{switch(a){case"warning":return g.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-yellow-100 rounded-full flex items-center justify-center",children:g.jsx("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:g.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})});case"danger":return g.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center",children:g.jsx("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:g.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})});default:return g.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-blue-100 rounded-full flex items-center justify-center",children:g.jsx("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:g.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}},f=()=>{switch(a){case"danger":return"danger";case"warning":return"primary";default:return"primary"}},y={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.1}}},w={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3,ease:"easeOut"}}};return g.jsx(ld,{isOpen:e,onClose:t,title:r,size:"sm",closeOnOverlayClick:!1,children:g.jsxs(J.div,{className:"text-center",variants:y,initial:"hidden",animate:"visible",children:[g.jsx(J.div,{variants:w,children:h()}),g.jsx(J.div,{className:"mt-4",variants:w,children:g.jsx("p",{className:"text-white leading-relaxed whitespace-pre-line",children:i})}),c&&g.jsx(J.div,{className:"mt-4",variants:w,children:c}),g.jsxs(J.div,{className:`mt-6 ${u==="corners"?"flex justify-between items-center":"flex flex-col-reverse sm:flex-row sm:gap-3"}`,variants:w,children:[g.jsx(zt,{onClick:t,variant:"secondary",className:u==="corners"?"px-6":"w-full sm:w-auto mt-3 sm:mt-0",disabled:l,children:o}),g.jsx(zt,{onClick:d,variant:f(),className:u==="corners"?"px-6":"w-full sm:w-auto",isLoading:l,autoFocus:!0,children:s})]})]})})},zb=({isOpen:e,onClose:t,onConfirm:n,title:r,message:i,placeholder:s="",defaultValue:o="",confirmText:a="OK",cancelText:l="Cancel",inputType:u="text",validation:c,isLoading:d=!1})=>{const[h,f]=S.useState(o),[y,w]=S.useState(null),x=S.useRef(null);S.useEffect(()=>{e&&(f(o),w(null),setTimeout(()=>{var E,b;(E=x.current)==null||E.focus(),(b=x.current)==null||b.select()},150))},[e,o]);const v=()=>{const E=h.trim();if(c){const b=c(E);if(b){w(b);return}}n(E)},p=E=>{E.key==="Enter"&&!E.shiftKey&&(E.preventDefault(),v())},m=E=>{const b=E.target.value;f(b),y&&w(null)},_=!y&&h.trim().length>0,k={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.1}}},C={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3,ease:"easeOut"}}};return g.jsx(ld,{isOpen:e,onClose:t,title:r,size:"md",closeOnOverlayClick:!1,children:g.jsxs(J.div,{variants:k,initial:"hidden",animate:"visible",children:[g.jsx(J.div,{className:"mb-4",variants:C,children:g.jsx("p",{className:"text-white leading-relaxed whitespace-pre-line",children:i})}),g.jsxs(J.div,{className:"mb-4",variants:C,children:[g.jsx(J.input,{ref:x,type:u,value:h,onChange:m,onKeyDown:p,placeholder:s,disabled:d,className:`
              w-full px-3 py-2 bg-background-primary border rounded-md text-white 
              placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors
              ${y?"border-red-500 focus:ring-red-500":"border-gray-600 focus:ring-primary-500"}
              disabled:opacity-50 disabled:cursor-not-allowed
            `,"aria-describedby":y?"input-error":void 0,"aria-invalid":!!y,"aria-required":"true","aria-label":s||"Input field",initial:{scale:.95},animate:{scale:1},transition:{delay:.2,duration:.2}}),y&&g.jsx(J.p,{id:"input-error",className:"mt-2 text-sm text-red-400",role:"alert",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.2},children:y})]}),g.jsxs(J.div,{className:"flex flex-col-reverse sm:flex-row sm:gap-3",variants:C,children:[g.jsx(zt,{onClick:t,variant:"secondary",className:"w-full sm:w-auto mt-3 sm:mt-0",disabled:d,children:l}),g.jsx(zt,{onClick:v,variant:"primary",className:"w-full sm:w-auto",disabled:!_,isLoading:d,children:a})]})]})})},Bb=({checked:e,onChange:t,disabled:n=!1,className:r=""})=>g.jsxs("div",{className:`flex items-center space-x-2 ${r}`,children:[g.jsx("input",{type:"checkbox",id:"never-ask-again",checked:e,onChange:i=>t(i.target.checked),disabled:n,className:"w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed"}),g.jsx("label",{htmlFor:"never-ask-again",className:`text-sm text-gray-300 select-none ${n?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:"Never ask again"})]}),Rv=S.createContext(void 0),WT=()=>{const e=S.useContext(Rv);if(!e)throw new Error("useDialog must be used within a DialogProvider");return e},Ub=({children:e})=>{const[t,n]=S.useState({type:null,isOpen:!1,options:null,resolve:null,isLoading:!1}),[r,i]=S.useState(!1),s=S.useCallback(()=>{n(w=>({...w,isOpen:!1,isLoading:!1})),setTimeout(()=>{n({type:null,isOpen:!1,options:null,resolve:null,isLoading:!1}),i(!1)},200)},[]),o=S.useCallback(w=>new Promise(x=>{n({type:"alert",isOpen:!0,options:w,resolve:x,isLoading:!1})}),[]),a=S.useCallback(w=>new Promise(x=>{n({type:"confirm",isOpen:!0,options:w,resolve:x,isLoading:!1})}),[]),l=S.useCallback(w=>new Promise(x=>{n({type:"prompt",isOpen:!0,options:w,resolve:x,isLoading:!1})}),[]),u=S.useCallback(()=>{t.resolve&&t.resolve(void 0),s()},[t.resolve,s]),c=S.useCallback(()=>{t.resolve&&t.resolve(!1),s()},[t.resolve,s]),d=S.useCallback(()=>{t.resolve&&t.resolve(!0),s()},[t.resolve,s]),h=S.useCallback(()=>{t.resolve&&t.resolve(null),s()},[t.resolve,s]),f=S.useCallback(w=>{t.resolve&&t.resolve(w),s()},[t.resolve,s]),y={alert:o,confirm:a,prompt:l,closeDialog:s};return g.jsxs(Rv.Provider,{value:y,children:[e,t.type==="alert"&&t.options&&g.jsx($b,{isOpen:t.isOpen,onClose:u,...t.options}),t.type==="confirm"&&t.options&&(()=>{const w=t.options,x=w.showNeverAskAgain?g.jsx(Bb,{checked:r,onChange:v=>{var p;i(v),(p=w.onNeverAskAgainChange)==null||p.call(w,v)},disabled:t.isLoading}):void 0;return g.jsx(Vb,{isOpen:t.isOpen,onClose:c,onConfirm:d,isLoading:t.isLoading,additionalContent:x,...w})})(),t.type==="prompt"&&t.options&&g.jsx(zb,{isOpen:t.isOpen,onClose:h,onConfirm:f,isLoading:t.isLoading,...t.options})]})},Fb=S.lazy(()=>Ve(()=>import("./Dashboard-6cc872a5.js"),["assets/Dashboard-6cc872a5.js","assets/index-20a4a298.js","assets/documentStore-f650591a.js"]).then(e=>({default:e.Dashboard}))),Hb=S.lazy(()=>Ve(()=>import("./DocumentsPage-c319fa8b.js"),["assets/DocumentsPage-c319fa8b.js","assets/index-20a4a298.js","assets/documentStore-f650591a.js"]).then(e=>({default:e.DocumentsPage}))),Wb=S.lazy(()=>Ve(()=>import("./CreateStudySetPage-fcae4ca4.js"),[]).then(e=>({default:e.CreateStudySetPage}))),Kb=S.lazy(()=>Ve(()=>import("./StudySetPage-6a150cd1.js"),["assets/StudySetPage-6a150cd1.js","assets/studyStore-eb933c64.js","assets/documentStore-f650591a.js","assets/DifficultySelector-a8f90e3b.js","assets/useUserSettings-b6794e0e.js"]).then(e=>({default:e.StudySetPage}))),Gb=S.lazy(()=>Ve(()=>import("./StudyPage-713ba2ee.js"),["assets/StudyPage-713ba2ee.js","assets/studyStore-eb933c64.js","assets/useUserSettings-b6794e0e.js"]).then(e=>({default:e.StudyPage}))),qb=S.lazy(()=>Ve(()=>import("./AnalyticsPage-5259a4ed.js"),["assets/AnalyticsPage-5259a4ed.js","assets/studyStore-eb933c64.js","assets/documentStore-f650591a.js"]).then(e=>({default:e.AnalyticsPage}))),Jb=S.lazy(()=>Ve(()=>import("./CreditsPage-576da7af.js"),[]).then(e=>({default:e.CreditsPage}))),Qb=S.lazy(()=>Ve(()=>import("./HelpPage-7a704395.js"),[]).then(e=>({default:e.HelpPage}))),Yb=S.lazy(()=>Ve(()=>import("./SettingsPage-9b98c4ba.js"),["assets/SettingsPage-9b98c4ba.js","assets/DifficultySelector-a8f90e3b.js"]).then(e=>({default:e.SettingsPage}))),Xb=()=>{const e=Cn(),{showShortcutsModal:t,setShowShortcutsModal:n}=Mb(),{checkAuth:r,isLoading:i,isAuthenticated:s}=Zr();S.useEffect(()=>{H_(),r()},[r]);const o=()=>{const a=e.pathname;return a==="/dashboard"?[{href:"#main-content",label:"Skip to main content"},{href:"#study-sets",label:"Skip to study sets list"},{href:"#create-study-set",label:"Skip to create study set"}]:a.startsWith("/study/")?[{href:"#main-content",label:"Skip to main content"},{href:"#flashcard",label:"Skip to flashcard"},{href:"#navigation-controls",label:"Skip to navigation controls"}]:a==="/documents"?[{href:"#main-content",label:"Skip to main content"},{href:"#upload-section",label:"Skip to upload section"},{href:"#document-list",label:"Skip to document list"}]:[{href:"#main-content",label:"Skip to main content"}]};return i?g.jsx("div",{className:"min-h-screen bg-background-primary flex items-center justify-center",children:g.jsxs("div",{className:"text-center",children:[g.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"}),g.jsx("p",{className:"text-gray-400",children:"Checking authentication..."})]})}):g.jsxs(g.Fragment,{children:[g.jsx(Ib,{links:o()}),g.jsx(S.Suspense,{fallback:g.jsx("div",{className:"min-h-screen bg-background-primary flex items-center justify-center",children:g.jsxs("div",{className:"text-center",children:[g.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"}),g.jsx("p",{className:"text-gray-400",children:"Loading..."})]})}),children:g.jsxs(Zw,{children:[g.jsx(Re,{path:"/",element:s?g.jsx(uu,{to:"/dashboard",replace:!0}):g.jsx(uu,{to:"/login",replace:!0})}),g.jsx(Re,{path:"/login",element:g.jsx(bb,{})}),g.jsx(Re,{path:"/signup",element:g.jsx(Tb,{})}),g.jsx(Re,{path:"/auth/callback",element:g.jsx(Pb,{})}),g.jsx(Re,{path:"/dashboard",element:g.jsx(kt,{children:g.jsx(Ct,{children:g.jsx(Fb,{})})})}),g.jsx(Re,{path:"/documents",element:g.jsx(kt,{children:g.jsx(Ct,{children:g.jsx(Hb,{})})})}),g.jsx(Re,{path:"/create-study-set",element:g.jsx(kt,{children:g.jsx(Ct,{children:g.jsx(Wb,{})})})}),g.jsx(Re,{path:"/study-sets/:id",element:g.jsx(kt,{children:g.jsx(Ct,{children:g.jsx(Kb,{})})})}),g.jsx(Re,{path:"/study/:id/:mode",element:g.jsx(kt,{children:g.jsx(Ct,{children:g.jsx(Gb,{})})})}),g.jsx(Re,{path:"/analytics",element:g.jsx(kt,{children:g.jsx(Ct,{children:g.jsx(qb,{})})})}),g.jsx(Re,{path:"/credits",element:g.jsx(kt,{children:g.jsx(Ct,{children:g.jsx(Jb,{})})})}),g.jsx(Re,{path:"/help",element:g.jsx(kt,{children:g.jsx(Ct,{children:g.jsx(Qb,{})})})}),g.jsx(Re,{path:"/settings",element:g.jsx(kt,{children:g.jsx(Ct,{children:g.jsx(Yb,{})})})})]})}),g.jsx(Av,{isOpen:t,onClose:()=>n(!1)}),g.jsx(Ab,{showWhenOnline:!0,position:"top"})]})};function Zb(){return g.jsx(ox,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:g.jsx(Ub,{children:g.jsx(Xb,{})})})}ml.createRoot(document.getElementById("root")).render(g.jsx(Fe.StrictMode,{children:g.jsx(Zb,{})}));export{mb as $,kT as A,zt as B,Eb as C,Sb as D,qf as E,vb as F,vT as G,bT as H,Er as I,IT as J,xb as K,pb as L,$T as M,ST as N,LT as O,CT as P,yT as Q,Fe as R,MT as S,jT as T,zT as U,Cb as V,DT as W,wT as X,AT as Y,HT as Z,Ve as _,WT as a,fT as a0,_T as a1,FT as a2,OT as a3,dT as a4,Zr as b,_m as c,ET as d,tT as e,gb as f,Jf as g,yb as h,xT as i,g as j,hb as k,mT as l,_b as m,J as n,UT as o,gT as p,RT as q,S as r,fb as s,BT as t,En as u,PT as v,pT as w,VT as x,NT as y,TT as z};
